/**
 * Shared Invoice Service for RVHelp monorepo
 * @module InvoiceService
 */

import { Invoice, InvoiceItem, InvoiceStatus, PrismaClient } from "@rvhelp/database";
import React from "react";
import { z } from "zod";
import { PlatformInvoiceNotificationEmail } from "./email-templates/PlatformInvoiceNotificationEmail";
import { SharedEmailService } from "./email.service";
import { SharedSlackService } from "./slack.service";

export interface InvoiceServiceInterface {
    createInvoice(data: CreateInvoiceInput): Promise<Invoice>;
    createPlatformInvoice(data: CreatePlatformInvoiceInput): Promise<Invoice>;
    getInvoiceById(id: string): Promise<Invoice | null>;
    getProviderInvoiceByWarrantyRequestId(warrantyRequestId: string): Promise<Invoice | null>;
    updateInvoiceStatus(id: string, status: InvoiceStatus): Promise<Invoice>;
    updateInvoice(id: string, data: any): Promise<Invoice>;
    updateInvoiceItem(itemId: string, data: {
        description?: string;
        quantity?: number;
        unit_price?: number;
    }): Promise<InvoiceItem>;
    getPendingPlatformInvoices(): Promise<Invoice[]>;
    approvePlatformInvoice(invoiceId: string): Promise<Invoice>;
    generatePlatformInvoice(originalInvoiceId: string): Promise<Invoice | null>;
}

export interface InvoiceServiceConfig {
    webAppUrl: string;
    portalAppUrl: string;
    platformFeeAmount?: number;
    notificationEmail?: string;
    teamEmails?: string[];
}

// Default configuration for platform invoice generation
const DEFAULT_PLATFORM_CONFIG = {
    platformFeeAmount: 5000, // $50.00 in cents
    notificationEmail: "<EMAIL>",
    teamEmails: ['<EMAIL>', '<EMAIL>']
} as const;

// Schemas
const createInvoiceSchema = z.object({
    provider_id: z.string().optional().describe("The listing ID of the service provider (optional for platform invoices)"),
    customer_name: z.string(),
    customer_email: z.string().email(),
    customer_phone: z.string().optional(),
    notes: z.string().optional(),
    due_date: z.date().optional().nullable(),
    items: z.array(
        z.object({
            description: z.string(),
            quantity: z.number().positive(),
            unit_price: z.number().int().positive()
        })
    ),
    status: z.nativeEnum(InvoiceStatus).optional()
});

const createPlatformInvoiceSchema = z.object({
    customer_name: z.string(),
    customer_email: z.string().email(),
    notes: z.string(),
    items: z.array(
        z.object({
            description: z.string(),
            quantity: z.number().positive(),
            unit_price: z.number().int().positive()
        })
    ),
    warranty_request_id: z.string(),
    status: z.nativeEnum(InvoiceStatus).optional()
});

export type CreateInvoiceInput = z.infer<typeof createInvoiceSchema>;
export type CreatePlatformInvoiceInput = z.infer<typeof createPlatformInvoiceSchema>;

export class InvoiceService implements InvoiceServiceInterface {
    constructor(
        private prisma: PrismaClient,
        private emailService?: SharedEmailService,
        private slackService?: SharedSlackService,
        private config?: InvoiceServiceConfig
    ) { }


    /**
     * Creates a new invoice in our database
     */
    async createInvoice(data: CreateInvoiceInput) {
        const validatedData = createInvoiceSchema.parse(data);

        // Verify that the listing exists if provider_id is provided
        if (validatedData.provider_id) {
            const listing = await this.prisma.listing.findUnique({
                where: { id: validatedData.provider_id }
            });

            if (!listing) {
                throw new Error("Listing not found");
            }
        }

        const totalAmount = validatedData.items.reduce(
            (sum, item) => sum + item.quantity * item.unit_price,
            0
        );

        return this.prisma.invoice.create({
            data: {
                provider_id: validatedData.provider_id,
                customer_name: validatedData.customer_name,
                customer_email: validatedData.customer_email,
                customer_phone: validatedData.customer_phone,
                due_date: validatedData.due_date,
                notes: validatedData.notes,
                amount: totalAmount,
                items: {
                    create: validatedData.items.map((item) => ({
                        description: item.description,
                        quantity: item.quantity,
                        unit_price: item.unit_price,
                        amount: item.quantity * item.unit_price
                    }))
                },
                status: validatedData.status || InvoiceStatus.DRAFT
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });
    }

    /**
     * Create a RV Help platform invoice
     */
    async createPlatformInvoice(data: CreatePlatformInvoiceInput) {
        const validatedData = createPlatformInvoiceSchema.parse(data);

        const totalAmount = validatedData.items.reduce(
            (sum, item) => sum + item.quantity * item.unit_price,
            0
        );

        const invoice = await this.prisma.invoice.create({
            data: {
                provider_id: null, // Platform invoices don't have a provider
                customer_name: validatedData.customer_name,
                customer_email: validatedData.customer_email,
                notes: validatedData.notes,
                due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
                amount: totalAmount,
                items: {
                    create: validatedData.items.map((item) => ({
                        description: item.description,
                        quantity: item.quantity,
                        unit_price: item.unit_price,
                        amount: item.quantity * item.unit_price
                    }))
                },
                status: validatedData.status || InvoiceStatus.DRAFT
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });

        // Attach the invoice_id to the warranty request
        await this.prisma.warrantyRequest.update({
            where: { id: validatedData.warranty_request_id },
            data: { platform_invoice_id: invoice.id }
        });

        return invoice;
    }

    /**
     * Gets a single invoice by ID
     */
    async getInvoiceById(id: string) {
        return this.prisma.invoice.findUnique({
            where: { id },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });
    }

    /**
     * Gets an invoice by warranty request ID (from provider_invoice_id relationship)
     */
    async getProviderInvoiceByWarrantyRequestId(warrantyRequestId: string) {
        // Get the warranty request
        const warrantyRequest = await this.prisma.warrantyRequest.findUnique({
            where: { id: warrantyRequestId }
        });

        if (!warrantyRequest || !warrantyRequest.provider_invoice_id) {
            throw new Error("Warranty request not found or does not have a provider invoice");
        }

        // Get the provider invoice
        const providerInvoice = await this.prisma.invoice.findUnique({
            where: {
                id: warrantyRequest.provider_invoice_id!
            },
            include: {
                items: true,
                provider: {
                    include: {
                        owner: true
                    }
                }
            }
        });

        return providerInvoice;
    }

    /**
     * Updates the status of an invoice
     */
    async updateInvoiceStatus(id: string, status: InvoiceStatus) {
        return this.prisma.invoice.update({
            where: { id },
            data: { status },
            include: {
                items: true
            }
        });
    }

    /**
     * Deletes an invoice
     */
    async deleteInvoice(id: string) {
        return this.prisma.invoice.delete({
            where: { id }
        });
    }

    /**
     * Updates an invoice
     */
    async updateInvoice(id: string, data: any) {
        // Extract items from the data if present
        const { items, ...invoiceData } = data;

        // Prepare the update data
        const updateData: any = { ...invoiceData };

        // If items are provided, handle them properly with Prisma's nested update syntax
        if (items && Array.isArray(items)) {
            // First, delete all existing items
            await this.prisma.invoiceItem.deleteMany({
                where: { invoice_id: id }
            });

            // Then create new items
            updateData.items = {
                create: items.map((item) => ({
                    description: item.description,
                    quantity: item.quantity,
                    unit_price: item.unit_price,
                    amount: item.amount
                }))
            };
        }

        return this.prisma.invoice.update({
            where: { id },
            data: updateData,
            include: {
                items: true
            }
        });
    }

    /**
     * Updates an invoice item
     */
    async updateInvoiceItem(
        itemId: string,
        data: {
            description?: string;
            quantity?: number;
            unit_price?: number;
        }
    ) {
        const item = await this.prisma.invoiceItem.findUnique({
            where: { id: itemId }
        });

        if (!item) {
            throw new Error("Invoice item not found");
        }

        const updatedQuantity = data.quantity ?? item.quantity;
        const updatedUnitPrice = data.unit_price ?? item.unit_price;
        const updatedAmount = updatedQuantity * updatedUnitPrice;

        return this.prisma.invoiceItem.update({
            where: { id: itemId },
            data: {
                ...data,
                amount: updatedAmount
            }
        });
    }

    /**
     * Gets all pending platform fee invoices for review
     */
    async getPendingPlatformInvoices() {
        try {
            const notificationEmail = this.config?.notificationEmail || "<EMAIL>";

            return await this.prisma.invoice.findMany({
                where: {
                    customer_email: notificationEmail,
                    status: InvoiceStatus.DRAFT,
                    notes: {
                        contains: "Platform fee invoice"
                    }
                },
                include: {
                    items: true,
                    warranty_platform_request: {
                        include: {
                            job: true
                        }
                    }
                },
                orderBy: {
                    created_at: 'desc'
                }
            });
        } catch (error) {
            console.error('Error getting pending platform invoices:', error);
            throw error;
        }
    }

    /**
     * Approves and sends a platform fee invoice
     */
    async approvePlatformInvoice(invoiceId: string) {
        try {
            // Update invoice status to SENT
            const approvedInvoice = await this.updateInvoice(invoiceId, {
                status: InvoiceStatus.SENT
            });

            console.log(`Approved and sent platform fee invoice ${invoiceId}`);

            return approvedInvoice;
        } catch (error) {
            console.error(`Error approving platform fee invoice ${invoiceId}:`, error);
            throw error;
        }
    }

    /**
     * Generates a platform fee invoice after an original invoice is paid
     */
    async generatePlatformInvoice(originalInvoiceId: string) {
        const platformFeeAmount = this.config?.platformFeeAmount || DEFAULT_PLATFORM_CONFIG.platformFeeAmount;
        const notificationEmail = this.config?.notificationEmail || DEFAULT_PLATFORM_CONFIG.notificationEmail;

        try {
            // Get the original invoice with all its details
            const originalInvoice = await this.getInvoiceById(originalInvoiceId);
            if (!originalInvoice) {
                throw new Error(`Original invoice ${originalInvoiceId} not found`);
            }

            // Find warranty request through the provider_invoice_id relationship
            const warrantyRequest = await this.prisma.warrantyRequest.findUnique({
                where: { provider_invoice_id: originalInvoiceId }
            });

            // Skip if this is not a warranty invoice
            if (!warrantyRequest) {
                console.log(`Invoice ${originalInvoiceId} is not a warranty invoice, skipping platform fee generation`);
                return null;
            }

            // Check if platform fee invoice already exists for this warranty request
            const existingPlatformInvoice = await this.prisma.invoice.findFirst({
                where: {
                    warranty_platform_request: {
                        id: warrantyRequest.id
                    },
                    customer_email: notificationEmail,
                    notes: {
                        contains: "Platform fee invoice"
                    }
                }
            });

            if (existingPlatformInvoice) {
                console.log(`Platform fee invoice already exists for warranty request ${warrantyRequest.id}`);
                return existingPlatformInvoice;
            }

            // Get the original invoice items
            const originalItems = await this.prisma.invoiceItem.findMany({
                where: { invoice_id: originalInvoiceId }
            });

            // Defensive check for originalItems
            if (!originalItems || !Array.isArray(originalItems)) {
                throw new Error(`No items found for original invoice ${originalInvoiceId}`);
            }

            if (!originalInvoice.provider_id) {
                console.log(`No provider ID found for original invoice ${originalInvoiceId}, skipping platform fee generation`);
                return null;
            }

            // Get provider details for line item descriptions and notifications
            const provider = await this.prisma.listing.findUnique({
                where: { id: originalInvoice.provider_id },
                select: {
                    business_name: true,
                    first_name: true,
                    last_name: true,
                    email: true
                }
            });

            const providerName = provider?.business_name ||
                (provider ? `${provider.first_name || ''} ${provider.last_name || ''}`.trim() : "Unknown Provider") || "Unknown Provider";

            // Prepare line items: copy all original items with provider name + add platform fee
            const platformFeeItems = [
                // Copy all original invoice items with provider name in parentheses
                ...originalItems.map(item => ({
                    description: `${item.description} (${providerName})`,
                    quantity: item.quantity,
                    unit_price: item.unit_price
                })),
                // Add platform fee item
                {
                    description: "RV Help Platform Fee - Warranty Claim Processing",
                    quantity: 1,
                    unit_price: platformFeeAmount
                }
            ];

            // Create the platform fee invoice
            const platformInvoice = await this.createPlatformInvoice({
                customer_name: "Keystone RV - Owner Relations",
                customer_email: notificationEmail,
                notes: `RV Help - Warranty Portal Invoice for warranty claim. Provider invoice id: ${originalInvoice.id}`,
                items: platformFeeItems,
                warranty_request_id: warrantyRequest.id,
                status: InvoiceStatus.DRAFT // Start as draft for approval
            });

            console.log(`Created platform fee invoice ${platformInvoice.id} for original invoice ${originalInvoiceId}`);

            // Automatically attach the platform invoice to the warranty request
            await this.attachPlatformInvoiceToWarrantyRequest(platformInvoice, warrantyRequest.id);

            // Get warranty request details for notifications

            const warrantyRequestDetails = await this.prisma.warrantyRequest.findUnique({
                where: { id: warrantyRequest.id },
                select: {
                    id: true,
                    rv_model: true,
                    rv_year: true
                }
            }) || undefined;

            // Send email notification if email service and template are provided
            if (this.emailService && PlatformInvoiceNotificationEmail && this.config && provider) {
                try {
                    const platformInvoiceUrl = `${this.config.webAppUrl}/api/invoices/${platformInvoice.id}/pdf`;
                    const teamEmails = this.config?.teamEmails || DEFAULT_PLATFORM_CONFIG.teamEmails;

                    console.log(`🔧 [Platform Invoice] Email notification - to: ${notificationEmail}, cc: ${teamEmails.join(', ')}`);

                    await this.emailService.send({
                        to: notificationEmail,
                        cc: teamEmails as string[],
                        subject: `RV Help - Warranty Portal Invoice #${platformInvoice.invoice_number} - ${originalInvoice.customer_name}`,
                        react: React.createElement(PlatformInvoiceNotificationEmail, {
                            platformInvoice: {
                                ...platformInvoice,
                                items: await this.prisma.invoiceItem.findMany({
                                    where: { invoice_id: platformInvoice.id }
                                })
                            } as any,
                            originalInvoice,
                            provider: provider as any,
                            platformInvoiceUrl
                        }),
                        emailType: "platform_invoice_notification"
                    });

                    console.log(`Platform invoice email notification sent for invoice ${platformInvoice.id}`);
                } catch (emailError) {
                    console.error(`Failed to send platform invoice email notification for ${platformInvoice.id}:`, emailError);
                }
            }

            // Send Slack notification for success
            if (this.slackService && provider) {
                // if this is local development, skip the Slack notification
                if (process.env.NODE_ENV === "development") {
                    console.log(`Skipping Slack notification for local development`);
                } else {
                    try {
                        await this.slackService.notifyPlatformInvoiceGenerated(
                            {
                                ...platformInvoice,
                                items: await this.prisma.invoiceItem.findMany({
                                    where: { invoice_id: platformInvoice.id }
                                })
                            },
                            originalInvoice,
                            {
                                ...provider,
                                email: provider.email || '<EMAIL>'
                            },
                            warrantyRequestDetails
                        );

                        console.log(`Platform invoice Slack notification sent for invoice ${platformInvoice.id}`);
                    } catch (slackError) {
                        console.error(`Failed to send platform invoice Slack notification for ${platformInvoice.id}:`, slackError);
                    }
                }
            }

            return platformInvoice;
        } catch (error) {
            console.error(`Error generating platform fee invoice for ${originalInvoiceId}:`, error);
            throw error;
        }
    }

    /**
     * Attaches the platform fee invoice to the warranty request as an attachment and sets the platform_invoice_id
     */
    private async attachPlatformInvoiceToWarrantyRequest(platformInvoice: any, warrantyRequestId: string) {
        try {
            // Get the current warranty request to access existing attachments
            const warrantyRequest = await this.prisma.warrantyRequest.findUnique({
                where: { id: warrantyRequestId }
            });

            if (!warrantyRequest) {
                console.error(`Warranty request ${warrantyRequestId} not found for platform invoice attachment`);
                return;
            }

            // Create the attachment object for the platform invoice
            const platformInvoiceAttachment = {
                id: `platform-invoice-${platformInvoice.id}`,
                type: "document" as const,
                title: "RV Help Invoice",
                url: `${this.config?.webAppUrl}/api/invoices/${platformInvoice.id}/pdf`,
                required: false,
                completed: true
            };

            // Get existing attachments and add the new platform invoice
            const existingAttachments = (warrantyRequest.attachments as any[]) || [];
            const updatedAttachments = [...existingAttachments, platformInvoiceAttachment];

            // Update the warranty request with the new attachment AND set the platform_invoice_id
            await this.prisma.warrantyRequest.update({
                where: { id: warrantyRequestId },
                data: {
                    attachments: updatedAttachments as any,
                    platform_invoice_id: platformInvoice.id
                }
            });

            console.log(`Attached platform invoice ${platformInvoice.id} to warranty request ${warrantyRequestId} and set platform_invoice_id`);
        } catch (error) {
            console.error(`Error attaching platform invoice to warranty request:`, error);
            // Don't throw here - we don't want to fail invoice generation if attachment fails
        }
    }
}
