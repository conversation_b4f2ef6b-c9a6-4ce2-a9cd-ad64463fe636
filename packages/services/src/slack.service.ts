/**
 * Shared Slack Service for RVHelp monorepo
 */

import { PrismaClient } from "@rvhelp/database";

interface SlackMessage {
    text?: string;
    blocks?: any[];
}

export interface SlackWebhooks {
    providerGrowth?: string;
    dailyStats?: string;
    josiahMann?: string;
    providerLeads?: string;
    memberSignups?: string;
    oemJobs?: string;
}

export class SharedSlackService {
    private webhooks: SlackWebhooks;
    private prisma: PrismaClient;

    constructor(prisma: PrismaClient, webhooks: SlackWebhooks) {
        this.prisma = prisma;
        this.webhooks = webhooks;
    }

    private async sendToWebhook(message: SlackMessage, webhookUrl: string): Promise<void> {
        if (!webhookUrl) return;

        try {
            const response = await fetch(webhookUrl, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify(message),
            });

            if (!response.ok) {
                console.error(`Slack webhook failed: ${response.status} ${response.statusText}`);
            }
        } catch (error) {
            console.error("Failed to send Slack message:", error);
        }
    }

    async notifyPlatformInvoiceGenerated(
        platformInvoice: any,
        originalInvoice: any,
        provider: any,
        warrantyRequest: any
    ): Promise<void> {
        const message: SlackMessage = {
            text: `🎯 Platform Invoice Generated`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "🎯 Platform Invoice Generated"
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Platform Invoice:* #${platformInvoice.invoice_number}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Original Invoice:* #${originalInvoice.invoice_number}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Provider:* ${provider.business_name || `${provider.first_name} ${provider.last_name}`}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Amount:* $${(platformInvoice.amount / 100).toFixed(2)}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:* ${originalInvoice.customer_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*RV:* ${warrantyRequest?.rv_year || 'N/A'} ${warrantyRequest?.rv_model || 'N/A'}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `Platform fee invoice has been automatically generated and is ready for review.`
                    }
                }
            ]
        };

        await this.sendToWebhook(message, this.webhooks.oemJobs || '');
        await this.sendToWebhook(message, this.webhooks.josiahMann || '');
    }

    async notifyProviderGrowth(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.providerGrowth || '');
    }

    async notifyDailyStats(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.dailyStats || '');
    }

    async notifyProviderLeads(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.providerLeads || '');
    }

    async notifyMemberSignups(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.memberSignups || '');
    }

    async notifyOemJobs(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.oemJobs || '');
    }

    async notifyJosiahMann(message: string): Promise<void> {
        await this.sendToWebhook({ text: message }, this.webhooks.josiahMann || '');
    }

    // Backward compatibility methods that match original API signatures
    async sendToJosiahMann(message: SlackMessage): Promise<void> {
        await this.sendToWebhook(message, this.webhooks.josiahMann || '');
    }

    async sendToProviderLeads(message: SlackMessage): Promise<void> {
        await this.sendToWebhook(message, this.webhooks.providerLeads || '');
    }

    async notifyNewMemberSignup(user: any, level: any, memberNumber: number): Promise<void> {
        const message: SlackMessage = {
            text: `🎉 New ${level} Member Signup`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🎉 New ${level} Member Signup`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Member:* ${user.first_name} ${user.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${user.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Member #:* ${memberNumber}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Level:* ${level}`
                        }
                    ]
                }
            ]
        };
        await this.sendToWebhook(message, this.webhooks.memberSignups || '');
    }

    async notifyProviderVerified(user: any, businessName: string, slug: string, memberNumber: number): Promise<void> {
        const message: SlackMessage = {
            text: `✅ Provider Verified`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: "✅ Provider Verified"
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Business:* ${businessName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Owner:* ${user.first_name} ${user.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${user.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Member #:* ${memberNumber}`
                        }
                    ]
                },
                {
                    type: "section",
                    text: {
                        type: "mrkdwn",
                        text: `*Profile:* https://rvhelp.com/providers/${slug}`
                    }
                }
            ]
        };
        await this.sendToWebhook(message, this.webhooks.providerGrowth || '');
    }

    // Advanced notification methods can be added here as needed
    async sendCustomNotification(
        message: SlackMessage,
        webhookType: keyof SlackWebhooks
    ): Promise<void> {
        const webhookUrl = this.webhooks[webhookType];
        if (webhookUrl) {
            await this.sendToWebhook(message, webhookUrl);
        }
    }

    async notifyNewWarrantyRequest(warrantyRequest: any, companyName: string): Promise<void> {
        const message: SlackMessage = {
            text: `🔧 New Warranty Request`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🔧 New Warranty Request`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Company:* ${companyName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:* ${warrantyRequest.first_name} ${warrantyRequest.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${warrantyRequest.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*RV:* ${warrantyRequest.rv_year || 'N/A'} ${warrantyRequest.rv_make || 'N/A'} ${warrantyRequest.rv_model || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*VIN:* ${warrantyRequest.rv_vin || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Component:* ${warrantyRequest.component?.name || 'N/A'}`
                        }
                    ]
                }
            ]
        };

        await this.sendToWebhook(message, this.webhooks.oemJobs || '');
    }

    async notifyWarrantyAuthorizationRequest(warrantyRequest: any, companyName: string): Promise<void> {
        const message: SlackMessage = {
            text: `🔍 Warranty Authorization Request`,
            blocks: [
                {
                    type: "header",
                    text: {
                        type: "plain_text",
                        text: `🔍 Warranty Authorization Request`
                    }
                },
                {
                    type: "section",
                    fields: [
                        {
                            type: "mrkdwn",
                            text: `*Company:* ${companyName}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Customer:* ${warrantyRequest.first_name} ${warrantyRequest.last_name}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Email:* ${warrantyRequest.email}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*RV:* ${warrantyRequest.rv_year || 'N/A'} ${warrantyRequest.rv_make || 'N/A'} ${warrantyRequest.rv_model || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Component:* ${warrantyRequest.component?.name || 'N/A'}`
                        },
                        {
                            type: "mrkdwn",
                            text: `*Status:* ${warrantyRequest.status || 'N/A'}`
                        }
                    ]
                }
            ]
        };

        await this.sendToWebhook(message, this.webhooks.oemJobs || '');
    }
}

// Mock service for testing
export const mockSlackService = {
    notifyPlatformInvoiceGenerated: async () => { },
    notifyProviderGrowth: async () => { },
    notifyDailyStats: async () => { },
    notifyProviderLeads: async () => { },
    notifyMemberSignups: async () => { },
    notifyOemJobs: async () => { },
    notifyJosiahMann: async () => { },
    sendToJosiahMann: async () => { },
    sendToProviderLeads: async () => { },
    notifyNewMemberSignup: async () => { },
    notifyProviderVerified: async () => { },
    sendCustomNotification: async () => { },
    notifyNewWarrantyRequest: async () => { },
    notifyWarrantyAuthorizationRequest: async () => { },
};

export function createSlackService(
    prisma: PrismaClient,
    webhooks: SlackWebhooks,
    useMock: boolean = false
): SharedSlackService | typeof mockSlackService {
    if (useMock || process.env.NODE_ENV === "test") {
        return mockSlackService;
    }
    return new SharedSlackService(prisma, webhooks);
}
