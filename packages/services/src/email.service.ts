/**
 * Shared Email Service for RVHelp monorepo
 */

import { render } from "@react-email/render";
import { PrismaClient } from "@rvhelp/database";
import crypto from "crypto";
import nodemailer from "nodemailer";
import React from "react";
import { Resend } from "resend";
import { DisposableEmailService } from "./disposable-email.service";
import { SafelistService, SafelistServiceConfig } from "./safelist.service";

export interface EmailOptions {
    to: string;
    replyTo?: string;
    cc?: string | string[];
    from?: string;
    subject: string;
    react: React.ReactElement;
    text?: string;
    emailType?: string;
    data?: Record<string, any>;
    attachments?: Array<{
        filename: string;
        content: Buffer;
        contentType?: string;
    }>;
}

// Individual typed methods provide better type safety than a single union type

export interface EmailServiceConfig {
    from: string;
    appUrl: string;
    allowedDomains: string[];
    allowedEmails: string[];
    resendApiKey?: string;
    isDevelopment?: boolean;
    formatToE164?: (phone: string | undefined) => string;
}

export interface MockEmailService {
    send: () => Promise<{ success: boolean; result?: any }>;
    batchSend: () => Promise<{ results: any[]; errors: any[] }>;
    sendPasswordResetEmail: (email: string, token: string, isMobile: boolean) => Promise<{ success: boolean; error?: any }>;
    sendVerificationEmail: () => Promise<{ success: boolean; error?: any }>;
    sendVerificationCompletedEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWelcomeEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationRequestedEmail: () => Promise<{ success: boolean; error?: any }>;
    sendMembershipWelcomeEmail: () => Promise<{ success: boolean; error?: any }>;
    sendServiceRequestPasswordSetupEmail: () => Promise<{ success: boolean; error?: any }>;
    sendNoResultsEmail: () => Promise<{ success: boolean; error?: any }>;
    getEmail: () => Promise<any>;
    sendMembershipUpgradeEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWarrantyRequestEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationApprovedEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationFeedbackRequestedEmail: () => Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationRejectedEmail: () => Promise<{ success: boolean; error?: any }>;
}

export class SharedEmailService {
    private resend!: Resend;
    private transporter!: nodemailer.Transporter;
    private prisma: PrismaClient;
    private config: EmailServiceConfig;
    private safelistService: SafelistService;

    constructor(prisma: PrismaClient, config: EmailServiceConfig) {
        this.prisma = prisma;
        this.config = config;

        // Initialize safelist service with proper config
        const safelistConfig: SafelistServiceConfig = {
            isDevelopment: config.isDevelopment || false,
            formatToE164: config.formatToE164 || ((phone: string | undefined) => phone || '')
        };
        this.safelistService = new SafelistService(prisma, safelistConfig);

        if (this.isLocalDevelopment()) {
            // Use local SMTP for local development
            this.transporter = nodemailer.createTransport({
                host: 'localhost',
                port: 1025,
                secure: false,
                tls: {
                    rejectUnauthorized: false,
                },
            });
        } else {
            // Use Resend for all non-local environments
            if (!config.resendApiKey) {
                throw new Error('RESEND_API_KEY is required for non-local environments');
            }
            this.resend = new Resend(config.resendApiKey);
        }
    }

    private isLocalDevelopment(): boolean {
        const url = this.config.appUrl.toLowerCase();
        return url.includes('localhost') ||
            url.includes('127.0.0.1') ||
            url.includes('rvhelp.test') ||
            url.includes('0.0.0.0');
    }

    // Individual methods provide better type safety and IDE support

    async send(emailData: EmailOptions): Promise<{ success: boolean; result?: any; error?: any }> {
        try {
            const { react, ...rest } = emailData;
            const html = await render(react);

            let result: any;

            // Add disposable email validation
            const disposableCheck = DisposableEmailService.validateEmail(emailData.to);
            if (!disposableCheck.isValid) {
                console.warn(`Email blocked: ${emailData.to} is a disposable email address`);
                return {
                    success: false,
                    error: disposableCheck.error
                };
            }
            // If this is local development, send the email using the local SMTP server
            if (this.isLocalDevelopment()) {
                // Local development: use local SMTP (MailPit) - no safelist restrictions
                result = await this.transporter.sendMail({
                    from: rest.from || this.config.from,
                    to: rest.to,
                    subject: rest.subject,
                    html: html,
                    text: rest.text,
                    cc: rest.cc,
                    replyTo: rest.replyTo || rest.from || this.config.from,
                    attachments: emailData.attachments,
                } as any);

                return {
                    success: true,
                    result
                };
            }

            // For non-local environments, use safelist service (handles production vs development logic internally)
            const isAllowed = await this.safelistService.isAllowed("EMAIL", emailData.to);

            if (!isAllowed) {
                console.warn(`Email blocked: ${emailData.to} is not in allowed list`);
                return {
                    success: false,
                    error: "Email blocked - not in allowed list"
                };
            }

            // Use Resend for all non-local environments
            result = await this.resend.emails.send({
                from: rest.from || this.config.from,
                to: rest.to,
                subject: rest.subject,
                html: html,
                text: rest.text,
                cc: rest.cc,
                reply_to: rest.replyTo || rest.from || this.config.from,
                attachments: emailData.attachments,
            } as any);




            if (result?.error) {
                console.error('Email service error:', result.error);
                throw new Error(result.error);
            }

            // Log email to database
            await this.logEmail(emailData, html, result);

            return { success: true, result };
        } catch (error) {
            console.error('Email sending failed:', error);

            // Log failed email attempt
            try {
                const html = await render(emailData.react);
                await this.logEmail(emailData, html, null, 'failed', error);
            } catch (logError) {
                console.error('Failed to log email error:', logError);
            }

            return {
                success: false,
                error: error instanceof Error ? error.message : 'Unknown error'
            };
        }
    }


    async batchSend(options: EmailOptions[]): Promise<{ success: boolean; results: any[]; errors: string[] }> {
        try {
            const results: any[] = [];
            const errors: string[] = [];

            if (options.length === 0) {
                return { success: true, results: [], errors: [] };
            }

            // Validate all emails first
            for (const option of options) {
                const disposableCheck = DisposableEmailService.validateEmail(option.to);
                if (!disposableCheck.isValid) {
                    errors.push(`Email blocked: ${option.to} is a disposable email address`);
                    continue;
                }

                const isAllowed = await this.safelistService.isAllowed("EMAIL", option.to);
                if (!this.config.isDevelopment && !isAllowed) {
                    errors.push(`Email blocked: ${option.to} is not in allowed list`);
                    continue;
                }
            }

            // Filter out invalid emails
            const validOptions = options.filter(option => {
                const disposableCheck = DisposableEmailService.validateEmail(option.to);
                if (!disposableCheck.isValid) return false;

                if (this.config.isDevelopment) return true;

                return this.safelistService.isAllowed("EMAIL", option.to);
            });

            if (validOptions.length === 0) {
                return { success: false, results: [], errors };
            }

            // Prepare batch email data
            const batchData = await Promise.all(validOptions.map(async (option) => {
                const html = await render(option.react);
                return {
                    from: option.from || this.config.from,
                    to: option.to,
                    subject: option.subject,
                    html: html,
                    text: option.text,
                    cc: option.cc,
                    reply_to: option.replyTo || option.from || this.config.from,
                    attachments: option.attachments?.map((attachment) => ({
                        filename: attachment.filename,
                        content: attachment.content.toString("base64"),
                        contentType: attachment.contentType
                    }))
                };
            }));

            let batchResult;
            if (this.config.isDevelopment) {
                // For development, send individual emails using nodemailer
                batchResult = await Promise.allSettled(batchData.map(async (data) => {
                    return await this.transporter.sendMail({
                        ...data,
                        attachments: validOptions.find(opt => opt.to === data.to)?.attachments
                    });
                }));
            } else {
                // Use Resend batch API for production
                batchResult = await this.resend.batch.send(batchData);
            }

            // Process results and log to database
            for (let i = 0; i < validOptions.length; i++) {
                const option = validOptions[i];
                const result = this.config.isDevelopment ?
                    (batchResult as PromiseSettledResult<any>[])[i] :
                    (batchResult as any).data?.[i];

                try {
                    const html = await render(option.react);
                    const isSuccess = this.config.isDevelopment ?
                        (batchResult as PromiseSettledResult<any>[])[i].status === 'fulfilled' :
                        !result?.error;

                    await this.prisma.emailOutbox.create({
                        data: {
                            to_email: option.to,
                            from_email: this.config.from,
                            subject: option.subject,
                            html: html,
                            text: option.text || "",
                            digest: crypto.createHash("sha256").update(html).digest("hex"),
                            status: isSuccess ? "sent" : "failed",
                            response: JSON.stringify(result),
                            params: JSON.stringify({ type: option.emailType || "batch" }),
                            send_date: new Date()
                        }
                    });

                    if (isSuccess) {
                        results.push(result);
                    } else {
                        const error = this.config.isDevelopment ?
                            ((batchResult as PromiseSettledResult<any>[])[i].status === 'rejected' ?
                                ((batchResult as PromiseSettledResult<any>[])[i] as PromiseRejectedResult).reason :
                                'Unknown error') :
                            result?.error;
                        errors.push(`Failed to send email to ${option.to}: ${error}`);
                    }
                } catch (dbError) {
                    console.error("Error logging email to database:", dbError);
                    errors.push(`Failed to log email for ${option.to}: ${dbError}`);
                }
            }

            return {
                success: errors.length === 0,
                results,
                errors
            };
        } catch (error) {
            console.error("Error in batch email send:", error);
            return {
                success: false,
                results: [],
                errors: [`Batch send error: ${error}`]
            };
        }
    }

    private isEmailAllowed(email: string): boolean {
        // Check if email is in allowed emails list
        if (this.config.allowedEmails.includes(email)) {
            return true;
        }

        // Check if email domain is in allowed domains list
        const domain = email.split('@')[1];
        return this.config.allowedDomains.includes(domain);
    }

    private async logEmail(
        emailData: EmailOptions,
        html: string,
        result: any,
        status: string = 'sent',
        error?: any
    ): Promise<void> {
        try {
            await this.prisma.emailOutbox.create({
                data: {
                    to_email: emailData.to,
                    from_email: emailData.from || this.config.from,
                    subject: emailData.subject,
                    html: html,
                    text: emailData.text || '',
                    digest: crypto.createHash('sha256').update(html).digest('hex'),
                    status: status,
                    response: JSON.stringify(result || error),
                    params: JSON.stringify({ type: emailData.emailType }),
                    send_date: new Date(),
                },
            });
        } catch (dbError) {
            console.error('Failed to log email to database:', dbError);
            // Don't throw here - email logging failure shouldn't fail the email send
        }
    }

    // Web app methods
    async sendVerificationEmail(
        email: string,
        verificationToken: any,
        isMobile?: boolean
    ): Promise<{ success: boolean; error?: any }> {
        // This method handles verification through queue workers, so it needs to be implemented per app
        // for now since it has app-specific queue logic
        throw new Error('sendVerificationEmail must be implemented by the consuming app - requires app-specific queue setup');
    }

    async sendPasswordResetEmail(
        email: string,
        token: string,
        isMobile: boolean
    ): Promise<{ success: boolean; error?: any }> {
        const { PasswordResetEmail, passwordResetText } = await import('./templates/PasswordResetEmail');

        const resetUrl = `${this.config.appUrl}${isMobile ? "/mobile" : ""}/reset-password?token=${token}`;

        return await this.send({
            to: email,
            from: this.config.from,
            subject: "Reset your password",
            react: React.createElement(PasswordResetEmail, { resetLink: resetUrl }),
            text: passwordResetText(resetUrl),
            emailType: "password_reset"
        });
    }

    async sendServiceRequestPasswordSetupEmail(params: {
        email: string;
        firstName: string;
        lastName: string;
        serviceRequestId: string;
        token: string;
        rvDetails?: {
            year?: string;
            make?: string;
            model?: string;
            type?: string;
        };
        category?: string;
        message?: string;
    }): Promise<{ success: boolean; error?: any }> {
        const { default: ServiceRequestPasswordSetupEmail, serviceRequestPasswordSetupText } = await import('./templates/ServiceRequestPasswordSetupEmail');

        const passwordSetupLink = `${this.config.appUrl}/service-requests/${params.serviceRequestId}/setup-password?token=${params.token}&email=${encodeURIComponent(params.email)}`;

        return await this.send({
            to: params.email,
            from: this.config.from,
            subject: "Set up your password to access your RV service request",
            react: React.createElement(ServiceRequestPasswordSetupEmail, {
                firstName: params.firstName,
                lastName: params.lastName,
                serviceRequestId: params.serviceRequestId,
                passwordSetupLink,
                rvDetails: params.rvDetails,
                category: params.category,
                message: params.message
            }),
            text: serviceRequestPasswordSetupText(
                params.firstName,
                params.lastName,
                params.serviceRequestId,
                passwordSetupLink,
                params.rvDetails,
                params.category
            ),
            emailType: "service_request_password_setup"
        });
    }

    async sendNoResultsEmail(data: Record<string, any>): Promise<{ success: boolean; error?: any }> {
        const { NoResultsEmail } = await import('./templates/NoResultsEmail');

        return await this.send({
            to: "<EMAIL>",
            from: this.config.from,
            cc: data.email,
            subject: "New Service Request - No Results Found",
            react: React.createElement(NoResultsEmail, {
                name: data?.name ?? "",
                email: data?.email ?? "",
                phone: data?.phone ?? "",
                location: `${data?.city}, ${data?.state}`,
                rvDetails: `${data?.unit_year} ${data?.unit_make} ${data?.unit_model}`,
                description: data?.description ?? "",
                category: data?.category ?? ""
            }),
            emailType: "no_results"
        });
    }

    async sendWelcomeEmail(user: any): Promise<{ success: boolean; error?: any }> {
        const { UserWelcomeEmail } = await import('./templates/UserWelcomeEmail');

        return await this.send({
            to: user.email,
            subject: "Welcome to RV Help!",
            react: React.createElement(UserWelcomeEmail, {
                firstName: user.first_name
            }),
            emailType: "welcome"
        });
    }

    async getEmail(emailId: string): Promise<any> {
        if (this.config.isDevelopment) {
            return null;
        }

        return await this.resend.emails.get(emailId);
    }

    async sendMembershipUpgradeEmail(params: {
        email: string;
        name: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; result?: any; error?: any }> {
        const { MembershipUpgradeEmail } = await import('./templates/MembershipUpgradeEmail');

        return await this.send({
            to: params.email,
            subject: `Your RV Help Membership Has Been Upgraded`,
            react: React.createElement(MembershipUpgradeEmail, {
                name: params.name,
                membershipLevel: params.membershipLevel
            }),
            text: `Congratulations! Your RV Help membership has been upgraded to ${params.membershipLevel}`,
            emailType: "membership_upgrade"
        });
    }

    async sendWarrantyAuthorizationRequestedEmail(params: {
        to: string;
        customerName: string;
        companyName: string;
        rvYear?: string;
        rvMake?: string;
        rvModel?: string;
        rvVin: string;
        estimatedHours: number;
        cause: string;
        correction: string;
        updateNotes?: string;
        componentName?: string;
        warrantyRequestId: string;
    }): Promise<{ success: boolean; error?: any }> {
        const { WarrantyAuthorizationRequestedEmail, warrantyAuthorizationRequestedText } = await import('./templates/WarrantyAuthorizationRequestedEmail');

        return await this.send({
            to: params.to,
            subject: `Authorization Request for Your ${params.companyName} Warranty Service`,
            react: React.createElement(WarrantyAuthorizationRequestedEmail, {
                customerName: params.customerName,
                companyName: params.companyName,
                rvYear: params.rvYear,
                rvMake: params.rvMake,
                rvModel: params.rvModel,
                rvVin: params.rvVin,
                estimatedHours: params.estimatedHours,
                cause: params.cause,
                correction: params.correction,
                updateNotes: params.updateNotes,
                componentName: params.componentName,
                warrantyRequestId: params.warrantyRequestId,
                rvhelpUrl: this.config.appUrl
            }),
            text: warrantyAuthorizationRequestedText({
                customerName: params.customerName,
                companyName: params.companyName,
                rvYear: params.rvYear,
                rvMake: params.rvMake,
                rvModel: params.rvModel,
                rvVin: params.rvVin,
                estimatedHours: params.estimatedHours,
                cause: params.cause,
                correction: params.correction,
                updateNotes: params.updateNotes,
                componentName: params.componentName,
                warrantyRequestId: params.warrantyRequestId,
                rvhelpUrl: this.config.appUrl
            }),
            emailType: "warranty_authorization_requested"
        });
    }

    // Portal app methods
    async sendWarrantyRequestEmail(warrantyRequest: any): Promise<{ success: boolean; error?: any }> {
        throw new Error('sendWarrantyRequestEmail must be implemented by the consuming app');
    }

    async sendWarrantyAuthorizationApprovedEmail(
        warrantyRequest: any,
        approvedHours?: number,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        throw new Error('sendWarrantyAuthorizationApprovedEmail must be implemented by the consuming app');
    }

    async sendWarrantyAuthorizationFeedbackRequestedEmail(
        warrantyRequest: any,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        throw new Error('sendWarrantyAuthorizationFeedbackRequestedEmail must be implemented by the consuming app');
    }

    async sendWarrantyAuthorizationRejectedEmail(
        warrantyRequest: any,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        throw new Error('sendWarrantyAuthorizationRejectedEmail must be implemented by the consuming app');
    }

    async sendMembershipWelcomeEmail(params: {
        email: string;
        name: string;
        setupPasswordUrl: string;
        membershipLevel: string;
    }): Promise<{ success: boolean; error?: any }> {
        const { MembershipWelcomeEmail } = await import('./templates/MembershipWelcomeEmail');

        return await this.send({
            to: params.email,
            subject: `Welcome to RV Help ${params.membershipLevel} Membership`,
            react: React.createElement(MembershipWelcomeEmail, {
                name: params.name,
                setupPasswordUrl: params.setupPasswordUrl,
                membershipLevel: params.membershipLevel
            }),
            text: `Welcome to RV Help ${params.membershipLevel} Membership! Please set up your password at ${params.setupPasswordUrl}`,
            emailType: "membership_welcome"
        });
    }

    // NOTE: The original email services had additional template-specific methods like:
    // - sendPasswordResetEmail(email, token, isMobile?)
    // - sendVerificationEmail(email, verificationToken, isMobile?)  
    // - sendServiceRequestPasswordSetupEmail(params)
    // - sendVerificationCompletedEmail(...)
    // - sendWelcomeEmail(...)
    // - sendWarrantyAuthorizationRequestedEmail(...)
    //
    // These methods were highly specific to each app's email templates.
    // For now, apps should continue using their existing email service files
    // or extend this shared service with their specific template methods.
}

// Mock service for testing
export const mockEmailService: MockEmailService = {
    send: async () => ({ success: true }),
    batchSend: async () => ({ results: [], errors: [] }),
    sendPasswordResetEmail: async (email: string, token: string, isMobile: boolean) => ({ success: true }),
    sendVerificationEmail: async () => ({ success: true }),
    sendVerificationCompletedEmail: async () => ({ success: true }),
    sendWelcomeEmail: async () => ({ success: true }),
    sendWarrantyAuthorizationRequestedEmail: async () => ({ success: true }),
    sendMembershipWelcomeEmail: async () => ({ success: true }),
    sendServiceRequestPasswordSetupEmail: async () => ({ success: true }),
    sendNoResultsEmail: async () => ({ success: true }),
    getEmail: async () => (null),
    sendMembershipUpgradeEmail: async () => ({ success: true }),
    sendWarrantyRequestEmail: async () => ({ success: true }),
    sendWarrantyAuthorizationApprovedEmail: async () => ({ success: true }),
    sendWarrantyAuthorizationFeedbackRequestedEmail: async () => ({ success: true }),
    sendWarrantyAuthorizationRejectedEmail: async () => ({ success: true }),
};

export function createEmailService(
    prisma: PrismaClient,
    config: EmailServiceConfig,
    useMock: boolean = false
): SharedEmailService | MockEmailService {
    if (useMock || process.env.NODE_ENV === "test" || process.env.USE_MOCK_EMAIL === "true") {
        return mockEmailService;
    }
    return new SharedEmailService(prisma, config);
}
