import { prisma as mockPrisma } from "@/tests/mocks/prisma-mock";
import { InvoiceStatus, TimelineEventType } from "@rvhelp/database";

const { invoiceService } = require("@/lib/services");

// Mock dependencies
// Mock the stripe module
jest.mock("@/lib/stripe", () => ({
    stripe: {
        webhooks: {
            constructEvent: jest.fn()
        }
    }
}));

// Mock admin logger
jest.mock("@/lib/services/admin-log.service", () => ({
    adminLogger: {
        logWebhookEvent: jest.fn(),
        log: jest.fn()
    }
}));

// Mock next/headers
const mockHeadersGet = jest.fn();
jest.mock("next/headers", () => ({
    headers: () => ({
        get: mockHeadersGet
    })
}));

// Import the webhook handler
import { POST } from "@/app/api/stripe/webhook/route";

describe("Stripe Webhook - Platform Invoice Generation", () => {
    const mockInvoiceId = "inv_warranty_123";
    const mockWarrantyRequestId = "warranty_123";

    const mockPaymentIntent = {
        id: "pi_test_123",
        status: "succeeded",
        amount: 25000,
        metadata: {
            provider_invoice_id: mockInvoiceId,
            type: "invoice_payment"
        }
    };

    const mockInvoice = {
        id: mockInvoiceId,
        warranty_request_id: mockWarrantyRequestId,
        status: InvoiceStatus.DRAFT,
        payment_intent_id: null,
        payment_transfer_id: null
    };

    const mockPlatformInvoice = {
        id: "inv_platform_123",
        invoice_number: 1002,
        provider_id: "provider_123",
        customer_name: "Keystone RV - Owner Relations",
        customer_email: "<EMAIL>",
        amount: 30000,
        status: InvoiceStatus.DRAFT,
        warranty_request_id: mockWarrantyRequestId,
        notes: "Platform fee invoice for warranty claim. Original invoice: #1001"
    };

    const mockStripeWebhookEvent = {
        id: "evt_test_123",
        type: "payment_intent.succeeded",
        data: {
            object: mockPaymentIntent
        }
    };

    beforeEach(() => {
        jest.clearAllMocks();

        // Mock headers function to return test signature
        mockHeadersGet.mockImplementation((key: string) => {
            if (key === "stripe-signature") {
                return "test_signature";
            }
            return null;
        });

        // Mock stripe webhook verification
        const { stripe } = require("@/lib/stripe");
        stripe.webhooks.constructEvent.mockReturnValue(mockStripeWebhookEvent);

        // Mock environment variable
        process.env.STRIPE_WEBHOOK_SECRET = "whsec_test_secret";
    });

    afterEach(() => {
        delete process.env.STRIPE_WEBHOOK_SECRET;
    });

    describe("Platform Invoice Generation on Payment Success", () => {
        // TODO: Re-enable when platform invoice generation is fixed
        it("should generate platform invoice after warranty invoice payment", async () => {
            // Mock invoice lookup for payment processing
            mockPrisma.invoice.findUnique.mockResolvedValue(mockInvoice);

            // Mock invoice update for payment processing
            mockPrisma.invoice.update.mockResolvedValue({
                ...mockInvoice,
                status: InvoiceStatus.PAID,
                payment_intent_id: mockPaymentIntent.id,
                payment_transfer_id: mockPaymentIntent.id
            });

            // Mock warranty request lookup
            mockPrisma.warrantyRequest.findUnique.mockResolvedValue({
                id: mockWarrantyRequestId,
                provider_invoice_id: mockInvoiceId
            });

            // Mock warranty request update
            mockPrisma.warrantyRequest.update.mockResolvedValue({
                id: mockWarrantyRequestId,
                status: "INVOICE_PAID"
            });

            // Mock timeline update creation
            mockPrisma.timelineUpdate.create.mockResolvedValue({
                id: "timeline_123",
                warranty_request_id: mockWarrantyRequestId,
                event_type: TimelineEventType.INVOICE_PAID,
                details: { notes: "Payment completed via Stripe (webhook)" },
                date: new Date(),
                updated_by_id: null
            });

            // Mock platform invoice generation
            (invoiceService.generatePlatformInvoice as jest.Mock).mockResolvedValue(
                mockPlatformInvoice
            );

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify invoice payment processing
            expect(mockPrisma.invoice.findUnique).toHaveBeenCalledWith({
                where: { id: mockInvoiceId },
                select: {
                    status: true,
                    payment_intent_id: true,
                    payment_transfer_id: true
                }
            });

            expect(mockPrisma.invoice.update).toHaveBeenCalledWith({
                where: { id: mockInvoiceId },
                data: {
                    status: InvoiceStatus.PAID,
                    payment_intent_id: mockPaymentIntent.id,
                    payment_transfer_id: mockPaymentIntent.id
                }
            });

            // Verify warranty request lookup
            expect(mockPrisma.warrantyRequest.findUnique).toHaveBeenCalledWith({
                where: { provider_invoice_id: mockInvoiceId }
            });

            // Verify warranty request status update
            expect(mockPrisma.warrantyRequest.update).toHaveBeenCalledWith({
                where: { id: mockWarrantyRequestId },
                data: { status: "INVOICE_PAID" }
            });

            // Verify timeline update
            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledWith({
                data: {
                    warranty_request_id: mockWarrantyRequestId,
                    event_type: TimelineEventType.INVOICE_PAID,
                    details: { notes: "Payment completed via Stripe (webhook)" },
                    date: expect.any(Date),
                    updated_by_id: null
                }
            });

            // Verify platform invoice generation
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith(mockInvoiceId);
        });

        // TODO: Re-enable when platform invoice generation is fixed
        it("should continue processing even if platform invoice generation fails", async () => {
            // Mock invoice lookup and updates as before
            mockPrisma.invoice.findUnique.mockResolvedValue(mockInvoice);
            mockPrisma.invoice.update.mockResolvedValue({
                ...mockInvoice,
                status: "PAID"
            });

            // Mock warranty request lookup
            mockPrisma.warrantyRequest.findUnique.mockResolvedValue({
                id: mockWarrantyRequestId,
                provider_invoice_id: mockInvoiceId
            });

            mockPrisma.warrantyRequest.update.mockResolvedValue({});
            mockPrisma.timelineUpdate.create.mockResolvedValue({});

            // Mock platform invoice generation failure
            (invoiceService.generatePlatformInvoice as jest.Mock).mockRejectedValue(
                new Error("Platform invoice generation failed")
            );

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            // Webhook should still succeed
            expect(response.status).toBe(200);

            // Verify main payment processing still occurred
            expect(mockPrisma.invoice.update).toHaveBeenCalled();
            expect(mockPrisma.warrantyRequest.update).toHaveBeenCalled();

            // Verify platform invoice generation was attempted
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith(mockInvoiceId);
        });

        it("should not generate platform invoice for non-warranty invoices", async () => {
            const nonWarrantyInvoice = {
                ...mockInvoice,
                warranty_request_id: null
            };

            mockPrisma.invoice.findUnique.mockResolvedValue(nonWarrantyInvoice);
            mockPrisma.invoice.update.mockResolvedValue({
                ...nonWarrantyInvoice,
                status: "PAID"
            });

            // Mock warranty request lookup - should return null for non-warranty invoice
            mockPrisma.warrantyRequest.findUnique.mockResolvedValue(null);

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify payment processing occurred
            expect(mockPrisma.invoice.update).toHaveBeenCalled();

            // Verify warranty request was NOT updated (no warranty_request_id)
            expect(mockPrisma.warrantyRequest.update).not.toHaveBeenCalled();

            // Verify platform invoice generation was NOT attempted
            expect(invoiceService.generatePlatformInvoice).not.toHaveBeenCalled();
        });

        it("should not generate platform invoice for already paid invoices", async () => {
            const alreadyPaidInvoice = {
                ...mockInvoice,
                status: "PAID"
            };

            mockPrisma.invoice.findUnique.mockResolvedValue(alreadyPaidInvoice);

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify no updates occurred for already paid invoice
            expect(mockPrisma.invoice.update).not.toHaveBeenCalled();
            expect(mockPrisma.warrantyRequest.update).not.toHaveBeenCalled();
            expect(invoiceService.generatePlatformInvoice).not.toHaveBeenCalled();
        });

        it("should handle missing invoice gracefully", async () => {
            mockPrisma.invoice.findUnique.mockResolvedValue(null);

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify no further processing occurred
            expect(mockPrisma.invoice.update).not.toHaveBeenCalled();
            expect(invoiceService.generatePlatformInvoice).not.toHaveBeenCalled();
        });

        it("should handle missing invoice ID in payment intent metadata", async () => {
            const paymentIntentWithoutInvoiceId = {
                ...mockPaymentIntent,
                metadata: {
                    type: "invoice_payment"
                    // Missing provider_invoice_id
                }
            };

            const eventWithoutInvoiceId = {
                ...mockStripeWebhookEvent,
                data: {
                    object: paymentIntentWithoutInvoiceId
                }
            };

            const { stripe } = require("@/lib/stripe");
            stripe.webhooks.constructEvent.mockReturnValue(eventWithoutInvoiceId);

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(eventWithoutInvoiceId),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify no invoice processing occurred
            expect(mockPrisma.invoice.findUnique).not.toHaveBeenCalled();
            expect(invoiceService.generatePlatformInvoice).not.toHaveBeenCalled();
        });

        // TODO: Re-enable when platform invoice generation is fixed
        it("should set platform_invoice_id on warranty request when platform invoice is generated", async () => {
            // Mock invoice lookup for payment processing
            mockPrisma.invoice.findUnique.mockResolvedValue(mockInvoice);

            // Mock invoice update for payment processing
            mockPrisma.invoice.update.mockResolvedValue({
                ...mockInvoice,
                status: InvoiceStatus.PAID,
                payment_intent_id: mockPaymentIntent.id,
                payment_transfer_id: mockPaymentIntent.id
            });

            // Mock warranty request lookup - the webhook looks for warranty request by provider_invoice_id
            mockPrisma.warrantyRequest.findUnique.mockResolvedValue({
                id: mockWarrantyRequestId,
                provider_invoice_id: mockInvoiceId,
                attachments: []
            });

            // Mock warranty request update for status change (first call)
            mockPrisma.warrantyRequest.update
                .mockResolvedValueOnce({
                    id: mockWarrantyRequestId,
                    status: "INVOICE_PAID"
                })
                // Mock warranty request update for platform_invoice_id (second call from generatePlatformInvoice)
                .mockResolvedValueOnce({
                    id: mockWarrantyRequestId,
                    status: "INVOICE_PAID",
                    platform_invoice_id: mockPlatformInvoice.id
                });

            // Mock timeline update creation
            mockPrisma.timelineUpdate.create.mockResolvedValue({
                id: "timeline_123",
                warranty_request_id: mockWarrantyRequestId,
                event_type: TimelineEventType.INVOICE_PAID,
                details: { notes: "Payment completed via Stripe (webhook)" },
                date: new Date(),
                updated_by_id: null
            });

            // Mock platform invoice generation - this should be called by the webhook
            (invoiceService.generatePlatformInvoice as jest.Mock).mockResolvedValue(
                mockPlatformInvoice
            );

            const mockRequest = new Request("http://localhost:3000/api/stripe/webhook", {
                method: "POST",
                body: JSON.stringify(mockStripeWebhookEvent),
                headers: {
                    "stripe-signature": "test_signature"
                }
            });

            const response = await POST(mockRequest);

            expect(response.status).toBe(200);

            // Verify that generatePlatformInvoice was called
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith(mockInvoiceId);

            // Verify that the warranty request was updated for status change
            expect(mockPrisma.warrantyRequest.update).toHaveBeenCalledWith({
                where: { id: mockWarrantyRequestId },
                data: { status: "INVOICE_PAID" }
            });

            // The platform_invoice_id should be set by the generatePlatformInvoice function
            // which calls attachPlatformInvoiceToWarrantyRequest internally
        });
    });
});