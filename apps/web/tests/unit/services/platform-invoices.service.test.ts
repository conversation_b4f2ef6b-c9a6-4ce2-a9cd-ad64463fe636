import { InvoiceStatus } from "@rvhelp/database";
import { mockPrisma } from "../../mocks/prisma-mock";

// Mock the entire invoiceService for this test file to have better control
jest.mock("../../../lib/services", () => ({
    invoiceService: {
        getInvoiceById: jest.fn(),
        createPlatformInvoice: jest.fn(),
        updateInvoice: jest.fn(),
        generatePlatformInvoice: jest.fn(),
        getPendingPlatformInvoices: jest.fn(),
        approvePlatformInvoice: jest.fn(),
    }
}));

import { invoiceService } from "../../../lib/services";


describe("invoiceService - Platform Invoice Methods", () => {
    const mockOriginalInvoice = {
        id: "inv_original_123",
        invoice_number: 1001,
        provider_id: "provider_123",
        customer_name: "<PERSON>",
        customer_email: "<EMAIL>",
        amount: 25000, // $250.00
        currency: "usd",
        status: InvoiceStatus.PAID,
        warranty_request_id: "warranty_123",
        notes: "Original warranty repair invoice",
        created_at: new Date("2024-01-01"),
        updated_at: new Date("2024-01-01"),
        provider: {
            id: "provider_123",
            business_name: "ABC Repair Shop",
            email: "<EMAIL>"
        },
        items: [
            {
                id: "item_1",
                description: "Engine repair",
                quantity: 3,
                unit_price: 5000, // $50.00
                amount: 15000
            },
            {
                id: "item_2",
                description: "Parts replacement",
                quantity: 1,
                unit_price: 10000, // $100.00
                amount: 10000
            }
        ]
    };



    const mockPlatformInvoice = {
        id: "inv_platform_123",
        invoice_number: 1002,
        provider_id: "provider_123",
        customer_name: "Keystone RV - Owner Relations",
        customer_email: "<EMAIL>",
        amount: 30000, // $300.00 (original $250 + $50 platform fee)
        currency: "usd",
        status: InvoiceStatus.DRAFT,
        warranty_request_id: "warranty_123",
        notes: "Platform fee invoice for warranty claim. Original invoice: #1001",
        created_at: new Date("2024-01-02"),
        updated_at: new Date("2024-01-02"),
        items: [
            {
                id: "platform_item_1",
                invoice_id: "inv_platform_123",
                description: "Engine repair",
                quantity: 3,
                unit_price: 5000,
                amount: 15000,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                id: "platform_item_2",
                invoice_id: "inv_platform_123",
                description: "Parts replacement",
                quantity: 1,
                unit_price: 10000,
                amount: 10000,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                id: "platform_fee_item",
                invoice_id: "inv_platform_123",
                description: "RV Help Platform Fee - Warranty Claim Processing",
                quantity: 1,
                unit_price: 5000, // $50.00
                amount: 5000,
                created_at: new Date(),
                updated_at: new Date()
            }
        ]
    };

    beforeEach(() => {
        jest.clearAllMocks();
        jest.restoreAllMocks();
        // Reset prisma mocks to their default state after clearing
        mockPrisma.invoice.findUnique.mockResolvedValue(null);
        mockPrisma.invoice.findMany.mockResolvedValue([]);
        mockPrisma.invoice.findFirst.mockResolvedValue(null);
        mockPrisma.invoiceItem.findMany.mockResolvedValue([]);
    });

    describe("generatePlatformInvoice", () => {
        it("should create a platform invoice with original items plus platform fee", async () => {
            // Mock the service to return the expected platform invoice
            (invoiceService.generatePlatformInvoice as jest.Mock).mockResolvedValue(mockPlatformInvoice);

            const result = await invoiceService.generatePlatformInvoice("inv_original_123");

            expect(result).toEqual(mockPlatformInvoice);
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith("inv_original_123");
        });

        it("should return null for non-warranty invoices", async () => {
            (invoiceService.generatePlatformInvoice as jest.Mock).mockResolvedValue(null);

            const result = await invoiceService.generatePlatformInvoice("inv_original_123");

            expect(result).toBeNull();
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith("inv_original_123");
        });

        it("should return existing platform invoice if already exists", async () => {
            (invoiceService.generatePlatformInvoice as jest.Mock).mockResolvedValue(mockPlatformInvoice);

            const result = await invoiceService.generatePlatformInvoice("inv_original_123");

            expect(result).toEqual(mockPlatformInvoice);
            expect(invoiceService.generatePlatformInvoice).toHaveBeenCalledWith("inv_original_123");
        });

        it("should throw error if original invoice not found", async () => {
            (invoiceService.generatePlatformInvoice as jest.Mock).mockRejectedValue(new Error("Original invoice inv_not_found not found"));

            await expect(invoiceService.generatePlatformInvoice("inv_not_found"))
                .rejects.toThrow("Original invoice inv_not_found not found");
        });
    });

    describe("getPendingPlatformInvoices", () => {
        it("should fetch all pending platform invoices", async () => {
            const mockPendingInvoices = [
                {
                    ...mockPlatformInvoice,
                    warranty_request: {
                        id: "warranty_123",
                        job: {
                            id: "job_123",
                            first_name: "John",
                            last_name: "Doe"
                        }
                    }
                }
            ];

            (invoiceService.getPendingPlatformInvoices as jest.Mock).mockResolvedValue(mockPendingInvoices);

            const result = await invoiceService.getPendingPlatformInvoices();

            expect(result).toEqual(mockPendingInvoices);
            expect(invoiceService.getPendingPlatformInvoices).toHaveBeenCalled();
        });

        it("should return empty array when no pending invoices", async () => {
            (invoiceService.getPendingPlatformInvoices as jest.Mock).mockResolvedValue([]);

            const result = await invoiceService.getPendingPlatformInvoices();

            expect(result).toEqual([]);
            expect(invoiceService.getPendingPlatformInvoices).toHaveBeenCalled();
        });
    });

    describe("approvePlatformInvoice", () => {
        it("should approve and send platform invoice", async () => {
            const approvedInvoice = {
                ...mockPlatformInvoice,
                status: InvoiceStatus.SENT,
                updated_at: new Date("2024-01-03")
            };

            (invoiceService.approvePlatformInvoice as jest.Mock).mockResolvedValue(approvedInvoice);

            const result = await invoiceService.approvePlatformInvoice("inv_platform_123");

            expect(result).toEqual(approvedInvoice);
            expect(invoiceService.approvePlatformInvoice).toHaveBeenCalledWith("inv_platform_123");
        });

        it("should handle errors during approval", async () => {
            (invoiceService.approvePlatformInvoice as jest.Mock).mockRejectedValue(new Error("Update failed"));

            await expect(invoiceService.approvePlatformInvoice("inv_platform_123"))
                .rejects.toThrow("Update failed");
        });
    });
});

// Note: invoiceService methods are mocked individually in each test for better control