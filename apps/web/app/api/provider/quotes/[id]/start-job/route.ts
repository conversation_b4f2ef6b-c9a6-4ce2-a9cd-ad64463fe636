import { createHand<PERSON> } from "@/lib/api/baseHandler";
import prisma from "@/lib/prisma";
import { QuoteStatusService } from "@/lib/services/quote-status.service";
import { NextResponse } from "next/server";


export const POST = createHandler({
    requireAuth: true,
    requiredRole: "PROVIDER",
    handler: async function (req, { validatedData, session, params }) {
        try {


            const quote = await prisma.quote.findUnique({
                where: { id: params.id },
            });


            if (!quote) {
                return this.respond("Quote not found", 404, {
                    message: "Quote not found"
                });
            }

            const message = "Provider accepted job for customer."
            const finalQuote = await QuoteStatusService.providerStartJob({
                quote: quote,
                userId: session.user.id,
            });

            return NextResponse.json(finalQuote, { status: 200 });

        } catch (error) {
            console.error("Error occurred:", error);
            // Re-throw the error so the createHandler can handle it properly
            throw error;
        }
    }
});
