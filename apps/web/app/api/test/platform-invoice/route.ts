
import prisma from "@/lib/prisma";
import { invoiceService } from "@/lib/services";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
    try {
        const { warrantyRequestId } = await request.json();

        if (!warrantyRequestId) {
            return NextResponse.json(
                { error: "warrantyRequestId is required" },
                { status: 400 }
            );
        }

        // Get the warranty request to find the provider invoice ID
        const warrantyRequest = await prisma.warrantyRequest.findUnique({
            where: { id: warrantyRequestId },
            select: {
                id: true,
                provider_invoice_id: true,
                first_name: true,
                last_name: true,
                email: true,
                rv_model: true,
                rv_year: true
            }
        });

        if (!warrantyRequest) {
            return NextResponse.json(
                { error: "Warranty request not found" },
                { status: 404 }
            );
        }

        if (!warrantyRequest.provider_invoice_id) {
            return NextResponse.json(
                { error: "Warranty request does not have an associated provider invoice" },
                { status: 400 }
            );
        }

        // Generate platform invoice with email template
        const platformInvoice = await invoiceService.generatePlatformInvoice(
            warrantyRequest.provider_invoice_id
        );

        if (!platformInvoice) {
            return NextResponse.json(
                { error: "Failed to generate platform invoice" },
                { status: 500 }
            );
        }

        return NextResponse.json({
            success: true,
            platformInvoice,
            message: "Platform invoice generated successfully"
        });

    } catch (error) {
        console.error("Error generating platform invoice:", error);
        return NextResponse.json(
            { error: "Internal server error", details: error instanceof Error ? error.message : "Unknown error" },
            { status: 500 }
        );
    }
}
