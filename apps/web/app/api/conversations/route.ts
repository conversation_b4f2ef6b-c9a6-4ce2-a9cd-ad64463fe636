// get all conversations
import { adminLogger } from "@/lib/services/admin-log.service";
import { NextResponse } from "next/server";
import { z } from "zod";
import { createHandler } from "../../../lib/api/baseHandler";
import { ListingService } from "../../../lib/services/listing.service";
import { MessageService } from "../../../lib/services/messaging.service";

export const GET = createHandler({
	validateQuery: z.object({
		context: z.enum(["provider", "user"]).default("user")
	}),
	requireAuth: true,
	handler: async (req, { session, query }) => {
		const { context } = query;

		let providerId = null;
		let userId = null;

		if (context === "provider") {
			const listing = await ListingService.getListingByUserId(session.user.id);
			providerId = listing.id;
		} else {
			userId = session.user.id;
		}

		if (!providerId && !userId) {
			return NextResponse.json(
				{ error: "No provider or user ID found" },
				{ status: 400 }
			);
		}

		try {
			const conversations = await MessageService.getConversations({
				providerId,
				userId,
				limit: 50,
				offset: 0
			});

			return NextResponse.json(conversations);
		} catch (error) {
			console.error("Error fetching conversations", error);
			adminLogger.log("Error fetching conversations", { error });
			return NextResponse.json(
				{ error: "Failed to fetch conversations" },
				{ status: 500 }
			);
		}
	}
});
