"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/lib/hooks/useAuth";
import { AlertTriangle, X } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "react-hot-toast";

export default function StripeAlertBanner() {
	const { user } = useAuth();
	const [needsUpdate, setNeedsUpdate] = useState(false);
	const [isLoading, setIsLoading] = useState(true);
	const [isDismissed, setIsDismissed] = useState(false);

	useEffect(() => {
		if (!user || user.role !== "PROVIDER") {
			setIsLoading(false);
			return;
		}

		const checkStripeStatus = async () => {
			try {
				const response = await fetch("/api/stripe/status");
				const data = await response.json();

				// Check if the account needs info update
				setNeedsUpdate(data.needsInfoUpdate || false);
			} catch (error) {
				console.error("Error checking Stripe status:", error);
				// Fail silently for the banner
			} finally {
				setIsLoading(false);
			}
		};

		checkStripeStatus();
	}, [user]);

	const handleUpdateAccount = async () => {
		try {
			const response = await fetch("/api/stripe/account-link", {
				method: "POST",
				headers: {
					"Content-Type": "application/json"
				},
				body: JSON.stringify({
					type: "account_update"
				})
			});

			if (!response.ok) {
				throw new Error("Failed to create account update link");
			}

			const data = await response.json();

			// Redirect to Stripe account update flow
			window.location.href = data.url;
		} catch (error) {
			console.error("Error creating account update link:", error);
			toast.error("Failed to create account update link. Please try again.");
		}
	};

	const handleDismiss = () => {
		setIsDismissed(true);
		// Store dismissal in sessionStorage so it stays dismissed during the session
		sessionStorage.setItem("stripeAlertDismissed", "true");
	};

	// Check if alert was previously dismissed this session
	useEffect(() => {
		const wasDismissed = sessionStorage.getItem("stripeAlertDismissed");
		if (wasDismissed) {
			setIsDismissed(true);
		}
	}, []);

	// Don't render if not a provider, still loading, doesn't need update, or was dismissed
	if (
		!user ||
		user.role !== "PROVIDER" ||
		isLoading ||
		!needsUpdate ||
		isDismissed
	) {
		return null;
	}

	return (
		<div className="bg-red-50 border-l-4 border-red-400 p-4">
			<div className="flex items-start">
				<div className="flex-shrink-0">
					<AlertTriangle className="h-5 w-5 text-red-400" />
				</div>
				<div className="ml-3 flex-1">
					<p className="text-sm text-red-700">
						<span className="font-semibold">
							Stripe Account Update Required:
						</span>{" "}
						Your account needs additional information to continue processing
						payouts. Update now to avoid payment delays.
					</p>
					<div className="mt-3 flex items-center gap-3">
						<Button
							size="sm"
							className="bg-red-600 hover:bg-red-700 text-white"
							onClick={handleUpdateAccount}
						>
							Update Stripe Account
						</Button>
						<Button
							size="sm"
							variant="outline"
							className="text-red-700 border-red-300 hover:bg-red-50"
							onClick={() =>
								(window.location.href = "/provider/billing/settings")
							}
						>
							View Details
						</Button>
					</div>
				</div>
				<div className="flex-shrink-0 ml-4">
					<button
						onClick={handleDismiss}
						className="inline-flex text-red-400 hover:text-red-600 focus:outline-none focus:text-red-600"
					>
						<span className="sr-only">Dismiss</span>
						<X className="h-5 w-5" />
					</button>
				</div>
			</div>
		</div>
	);
}
