import { formatCurrency } from '@/lib/utils';
import {
    Document,
    Image,
    Page,
    StyleSheet,
    Text,
    View
} from "@react-pdf/renderer";
import type { Style } from "@react-pdf/types";
import { format } from 'date-fns';

interface PlatformInvoicePDFProps {
    platformInvoice: {
        id: string;
        invoice_number: number;
        amount: number;
        currency: string;
        customer_name: string;
        customer_email: string;
        notes?: string;
        created_at: Date;
        due_date?: Date;
        items: Array<{
            description: string;
            quantity: number;
            unit_price: number;
            amount: number;
        }>;
    };
    warrantyRequest?: {
        id: string;
        rv_model?: string;
        rv_year?: string;
        first_name: string;
        last_name: string;
        email: string;
    };
    provider: {
        business_name: string;
        first_name: string;
        last_name: string;
        email: string;
    };
}

// Create styles
const styles = StyleSheet.create({
    page: {
        flexDirection: "column",
        backgroundColor: "#ffffff",
        padding: 40,
        fontFamily: "Helvetica",
        color: "#374151"
    } as Style,
    headerContainer: {
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: 40
    } as Style,
    logoContainer: {
        flexDirection: "column",
        width: "50%",
        height: 60,
        justifyContent: "center"
    } as Style,
    logo: {
        maxWidth: 220,
        maxHeight: 70,
        objectFit: "contain",
        objectPosition: "left",
        marginBottom: 10
    } as Style,
    logoText: {
        fontSize: 24,
        fontWeight: "bold",
        marginBottom: 8,
        color: "#111827"
    } as Style,
    invoiceInfoContainer: {
        flexDirection: "column",
        alignItems: "flex-end"
    } as Style,
    invoiceTitle: {
        fontSize: 28,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 10
    } as Style,
    invoiceInfo: {
        fontSize: 14,
        color: "#6b7280",
        marginBottom: 5
    } as Style,
    section: {
        marginBottom: 30
    } as Style,
    sectionTitle: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 10,
        textTransform: "uppercase"
    } as Style,
    sectionContent: {
        fontSize: 14,
        color: "#374151",
        marginBottom: 5
    } as Style,
    table: {
        display: "flex",
        width: "auto",
        borderStyle: "solid",
        borderWidth: 1,
        borderColor: "#e5e7eb"
    } as Style,
    tableRow: {
        margin: "auto",
        flexDirection: "row"
    } as Style,
    tableColHeader: {
        width: "25%",
        borderStyle: "solid",
        borderRightWidth: 1,
        borderBottomWidth: 1,
        borderColor: "#e5e7eb",
        backgroundColor: "#f9fafb"
    } as Style,
    tableCol: {
        width: "25%",
        borderStyle: "solid",
        borderRightWidth: 1,
        borderBottomWidth: 1,
        borderColor: "#e5e7eb"
    } as Style,
    tableCellHeader: {
        margin: "auto",
        marginTop: 5,
        marginBottom: 5,
        marginLeft: 10,
        marginRight: 10,
        fontSize: 12,
        fontWeight: "bold",
        color: "#111827"
    } as Style,
    tableCell: {
        margin: "auto",
        marginTop: 5,
        marginBottom: 5,
        marginLeft: 10,
        marginRight: 10,
        fontSize: 12,
        color: "#374151"
    } as Style,
    totalSection: {
        marginTop: 20,
        alignItems: "flex-end"
    } as Style,
    totalText: {
        fontSize: 16,
        fontWeight: "bold",
        color: "#111827"
    } as Style,
    notesSection: {
        marginTop: 30,
        padding: 20,
        backgroundColor: "#f9fafb"
    } as Style,
    notesTitle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#111827",
        marginBottom: 10
    } as Style,
    notesText: {
        fontSize: 12,
        color: "#6b7280",
        lineHeight: 1.5
    } as Style
});

export function PlatformInvoicePDF({
    platformInvoice,
    warrantyRequest,
    provider
}: PlatformInvoicePDFProps) {
    const providerName = provider.business_name || `${provider.first_name} ${provider.last_name}`;
    const customerName = warrantyRequest ? `${warrantyRequest.first_name} ${warrantyRequest.last_name}` : platformInvoice.customer_name;

    return (
        <Document>
            <Page size="A4" style={styles.page}>
                {/* Header with RV Help Logo */}
                <View style={styles.headerContainer}>
                    <View style={styles.logoContainer}>
                        <Image src="https://rvhelp.com/logos/logo.png" style={styles.logo} />
                        <Text style={styles.invoiceInfo}>RV Help Warranty Claim</Text>
                    </View>
                    <View style={styles.invoiceInfoContainer}>
                        <Text style={styles.invoiceTitle}>INVOICE</Text>
                        <Text style={styles.invoiceInfo}>Invoice #: {platformInvoice.invoice_number}</Text>
                        <Text style={styles.invoiceInfo}>Issue Date: {format(new Date(platformInvoice.created_at), 'MMMM d, yyyy')}</Text>
                        {platformInvoice.due_date && (
                            <Text style={styles.invoiceInfo}>Due Date: {format(new Date(platformInvoice.due_date), 'MMMM d, yyyy')}</Text>
                        )}
                    </View>
                </View>

                {/* Customer and Provider Information */}
                <View style={styles.section}>
                    <View style={{ flexDirection: "row", justifyContent: "space-between" }}>
                        <View style={{ flex: 1 }}>
                            <Text style={styles.sectionTitle}>Customer Name</Text>
                            <Text style={styles.sectionContent}>{customerName}</Text>
                            {warrantyRequest && (
                                <Text style={styles.sectionContent}>Email: {warrantyRequest.email}</Text>
                            )}
                        </View>
                        <View style={{ flex: 1 }}>
                            <Text style={styles.sectionTitle}>Provider Name</Text>
                            <Text style={styles.sectionContent}>{providerName}</Text>
                            <Text style={styles.sectionContent}>Email: {provider.email}</Text>
                        </View>
                    </View>
                </View>

                {/* Line Items Table */}
                <View style={styles.table}>
                    {/* Table Header */}
                    <View style={styles.tableRow}>
                        <View style={styles.tableColHeader}>
                            <Text style={styles.tableCellHeader}>Description</Text>
                        </View>
                        <View style={styles.tableColHeader}>
                            <Text style={styles.tableCellHeader}>Quantity</Text>
                        </View>
                        <View style={styles.tableColHeader}>
                            <Text style={styles.tableCellHeader}>Price</Text>
                        </View>
                        <View style={[styles.tableColHeader, { borderRightWidth: 0 }]}>
                            <Text style={styles.tableCellHeader}>Amount</Text>
                        </View>
                    </View>

                    {/* Table Rows */}
                    {platformInvoice.items.map((item, index) => (
                        <View key={index} style={styles.tableRow}>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{item.description}</Text>
                            </View>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{item.quantity}</Text>
                            </View>
                            <View style={styles.tableCol}>
                                <Text style={styles.tableCell}>{formatCurrency(item.unit_price)}</Text>
                            </View>
                            <View style={[styles.tableCol, { borderRightWidth: 0 }]}>
                                <Text style={styles.tableCell}>{formatCurrency(item.amount)}</Text>
                            </View>
                        </View>
                    ))}
                </View>

                {/* Total */}
                <View style={styles.totalSection}>
                    <Text style={styles.totalText}>Total: {formatCurrency(platformInvoice.amount)}</Text>
                </View>

                {/* Notes */}
                {platformInvoice.notes && (
                    <View style={styles.notesSection}>
                        <Text style={styles.notesTitle}>Notes</Text>
                        <Text style={styles.notesText}>{platformInvoice.notes}</Text>
                        <Text style={styles.notesText}>This is an automated platform fee invoice generated by RV Help</Text>
                        <Text style={styles.notesText}>For questions, please contact RV Help support</Text>
                    </View>
                )}
            </Page>
        </Document>
    );
}
