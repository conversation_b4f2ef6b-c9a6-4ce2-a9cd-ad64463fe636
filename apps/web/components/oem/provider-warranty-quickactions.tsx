import { Button } from "@/components/ui/button";
import { ExtendedWarrantyRequest } from "@/types/warranty";
import { FileEdit } from "lucide-react";

interface QuickActionsProps {
	onRequestAuthorization?: () => void;
	onRequestPayment?: () => void;
	onConfigureWarrantyPricing?: () => void;
	// onDocumentUpdate?: () => void;
	// onWarrantyUpdate?: () => void;
	warrantyRequest: ExtendedWarrantyRequest;
}

export function ProviderWarrantyQuickActions({
	warrantyRequest,
	onRequestAuthorization,
	onRequestPayment,
	onConfigureWarrantyPricing
	// onDocumentUpdate,
	// onWarrantyUpdate,
}: QuickActionsProps) {
	// const requiredAttachments = useMemo(() => {
	// 	const attachments =
	// 		(warrantyRequest?.attachments as WarrantyAttachment[]) || [];
	// 	return attachments.filter((attachment) => attachment.required);
	// }, [warrantyRequest?.attachments]);

	return (
		<>
			{/* WARRANTY ONLY - Update warranty status */}
			{warrantyRequest && (
				<>
					<div className="border-t border-gray-200 my-4"></div>
					<Button
						variant="outline"
						className="w-full"
						disabled={
							warrantyRequest.status !== "JOB_STARTED" &&
							!warrantyRequest.status.startsWith("AUTHORIZATION") &&
							warrantyRequest.status !== "PARTS_ORDERED"
						}
						onClick={onRequestAuthorization}
					>
						<FileEdit className="mr-2 h-4 w-4" />
						Submit Cause and Correction
					</Button>
					<Button
						variant="outline"
						className="w-full"
						disabled={
							warrantyRequest.status !== "AUTHORIZATION_APPROVED" &&
							warrantyRequest.status !== "PARTS_ORDERED"
						}
						onClick={onRequestPayment}
					>
						<FileEdit className="mr-2 h-4 w-4" />
						Create Invoice
					</Button>
					{/* {requiredAttachments.length > 0 && onDocumentUpdate && (
                        <Button
                            variant="outline"
                            className="w-full"
                            onClick={onDocumentUpdate}
                        >
                            <FileText className="mr-2 h-4 w-4" />
                            Complete Required Forms
                        </Button>
                    )} */}
				</>
			)}
		</>
	);
}
