import { BaseEmail } from "@/components/email-templates/BaseEmail";
import { Button, Section, Text } from "@react-email/components";
import { emailStyles } from "./shared-styles";

interface WarrantyAuthorizationRequestedEmailProps {
	customerName: string;
	companyName: string;
	rvYear?: string;
	rvMake?: string;
	rvModel?: string;
	rvVin: string;
	estimatedHours: number;
	cause: string;
	correction: string;
	updateNotes?: string;
	componentName?: string;
	warrantyRequestId: string;
	rvhelpUrl: string;
}

export function WarrantyAuthorizationRequestedEmail({
	customerName,
	companyName,
	rvYear,
	rvMake,
	rvModel,
	rvVin,
	estimatedHours,
	cause,
	correction,
	updateNotes,
	componentName,
	warrantyRequestId,
	rvhelpUrl
}: WarrantyAuthorizationRequestedEmailProps) {
	const previewText =
		"Your technician has requested additional warranty authorization";
	// Extract last 6 digits of VIN
	const lastSixDigits = rvVin.slice(-6);

	return (
		<BaseEmail previewText={previewText}>
			<Section>
				<Text style={emailStyles.heading}>
					⏳ Authorization Request for Your Warranty Service
				</Text>

				<Text style={emailStyles.text}>Dear {customerName},</Text>

				<Text style={emailStyles.text}>
					Your technician working on your {companyName}{" "}
					{rvModel && `${rvModel} `}has requested additional authorization to
					complete your warranty service.
				</Text>

				{/* Service details section */}
				<Text style={emailStyles.subheading}>
					Authorization Request Details:
				</Text>

				<Section style={emailStyles.messageBox}>
					<Text style={emailStyles.text}>
						<strong>Estimated Additional Hours:</strong> {estimatedHours}
					</Text>
					<Text style={emailStyles.text}>
						<strong>Issue Identified:</strong> {cause}
					</Text>
					<Text style={emailStyles.text}>
						<strong>Proposed Solution:</strong> {correction}
					</Text>
					{updateNotes && (
						<Text style={emailStyles.text}>
							<strong>Additional Notes:</strong> {updateNotes}
						</Text>
					)}
				</Section>

				{/* What happens next section */}
				<Text style={emailStyles.subheading}>What Happens Next?</Text>

				<Text style={emailStyles.text}>
					{companyName} will review this authorization request and respond
					promptly. You'll receive an email notification once a decision has
					been made.
				</Text>

				<Text style={emailStyles.text}>
					<strong>No action is required from you at this time.</strong> Your
					technician will coordinate directly with {companyName} regarding this
					request.
				</Text>

				{/* Service information section */}
				<Section style={emailStyles.messageBox}>
					<Text style={emailStyles.subheading}>Service Reference</Text>
					<Text style={emailStyles.text}>
						<strong>RV:</strong>{" "}
						{[rvYear, rvMake, rvModel].filter(Boolean).join(" ")} (VIN ending in{" "}
						{lastSixDigits})
					</Text>
					{componentName && (
						<Text style={emailStyles.text}>
							<strong>Component:</strong> {componentName}
						</Text>
					)}
					<Text style={emailStyles.text}>
						<strong>Request ID:</strong> {warrantyRequestId}
					</Text>
				</Section>

				{/* Action button */}
				<Section style={{ textAlign: "center" as const }}>
					<Button
						href={`${rvhelpUrl}/service-requests/${warrantyRequestId}`}
						style={emailStyles.button}
					>
						View Service Details
					</Button>
				</Section>

				{/* Footer */}
				<Text style={emailStyles.smallText}>
					This authorization request was submitted automatically by your
					technician through the RV Help platform. If you have questions about
					this request, please contact {companyName} directly.
				</Text>

				<Text style={emailStyles.smallText}>
					<strong>Need help?</strong> Contact RV Help support at{" "}
					<a
						href="mailto:<EMAIL>"
						style={{ color: "#437F6B", textDecoration: "underline" }}
					>
						<EMAIL>
					</a>
				</Text>
			</Section>
		</BaseEmail>
	);
}

export const warrantyAuthorizationRequestedText = (
	props: WarrantyAuthorizationRequestedEmailProps
) => {
	const lastSixDigits = props.rvVin.slice(-6);

	return `
Authorization Request for Your Warranty Service

Dear ${props.customerName},

Your technician working on your ${props.companyName} ${props.rvModel || ""} has requested additional authorization to complete your warranty service.

Authorization Request Details:
- Estimated Additional Hours: ${props.estimatedHours}
- Issue Identified: ${props.cause}
- Proposed Solution: ${props.correction}
${props.updateNotes ? `- Additional Notes: ${props.updateNotes}` : ""}

What Happens Next?
${props.companyName} will review this authorization request and respond promptly. You'll receive an email notification once a decision has been made.

No action is required from you at this time. Your technician will coordinate directly with ${props.companyName} regarding this request.

Service Reference:
- RV: ${[props.rvYear, props.rvMake, props.rvModel].filter(Boolean).join(" ")} (VIN ending in ${lastSixDigits})
${props.componentName ? `- Component: ${props.componentName}` : ""}
- Request ID: ${props.warrantyRequestId}

View Service Details: ${props.rvhelpUrl}/service-requests/${props.warrantyRequestId}

This authorization request was submitted automatically by your technician through the RV Help platform. If you have questions about this request, please contact ${props.companyName} directly.

Need help? Contact RV Help <NAME_EMAIL>
	`.trim();
};
