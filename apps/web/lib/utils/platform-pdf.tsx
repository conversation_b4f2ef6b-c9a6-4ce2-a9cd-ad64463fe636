/// <reference types="node" />
import { PlatformInvoicePDF } from "@/components/pdf/PlatformInvoicePDF";
import { pdf } from "@react-pdf/renderer";
import { Invoice, InvoiceItem } from "@rvhelp/database";
import { Readable } from "stream";

// Define the InvoiceWithItems interface
interface InvoiceWithItems extends Invoice {
    items: InvoiceItem[];
}

export async function generatePlatformInvoicePDF(
    invoice: Invoice,
    items: InvoiceItem[]
): Promise<Buffer> {
    // Create an invoice with items to match the InvoiceWithItems interface
    const invoiceWithItems: InvoiceWithItems = {
        ...invoice,
        items
    };


    // Fetch warranty request data - check both platform and provider invoice relationships
    const warrantyRequest = await prisma.warrantyRequest.findFirst({
        where: {
            OR: [
                { platform_invoice_id: invoice.id },
            ]
        },
        select: {
            id: true,
            first_name: true,
            last_name: true,
            email: true,
            rv_model: true,
            rv_year: true,
            rv_make: true,
            complaint: true,
            status: true,
            created_at: true
        }
    });


    const providerInvoice = await prisma.invoice.findFirst({
        where: {
            warranty_provider_request: {
                id: warrantyRequest.id
            }
        },
        select: {
            invoice_number: true,
            amount: true,
            customer_name: true,
            customer_email: true,
            provider_id: true
        }
    });

    const provider = await prisma.listing.findUnique({
        where: {
            id: providerInvoice.provider_id
        }
    });




    // Use real data or fall back to defaults
    const pdfDoc = (
        <PlatformInvoicePDF
            platformInvoice={invoiceWithItems}
            originalInvoice={providerInvoice || {
                invoice_number: 0,
                amount: 0,
                customer_name: "Unknown",
                customer_email: "<EMAIL>"
            }}
            warrantyRequest={warrantyRequest || {
                id: "unknown",
                first_name: "Unknown",
                last_name: "Customer",
                email: "<EMAIL>"
            }}
            provider={provider || {
                business_name: "Unknown Provider",
                first_name: "Unknown",
                last_name: "Provider",
                email: "<EMAIL>"
            }}
        />
    );
    const stream = await pdf(pdfDoc).toBuffer();

    // Convert ReadableStream to Node.js Readable stream
    const nodeStream = Readable.from(stream);

    // Convert stream to buffer
    return new Promise((resolve, reject) => {
        const chunks: Buffer[] = [];
        nodeStream.on("data", (chunk) => chunks.push(Buffer.from(chunk)));
        nodeStream.on("end", () => resolve(Buffer.concat(chunks)));
        nodeStream.on("error", reject);
    });
}
