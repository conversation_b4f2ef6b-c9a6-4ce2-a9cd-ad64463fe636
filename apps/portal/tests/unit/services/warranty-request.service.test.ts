import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { TimelineEventType } from '@rvhelp/database';
import prisma from '../../../../lib/prisma';
import { WarrantyRequestService } from '../../../../lib/services/warranty-request.service';

// Mock Prisma
jest.mock('@/lib/prisma', () => ({
    warrantyRequest: {
        create: jest.fn(),
        findUnique: jest.fn(),
        findFirst: jest.fn(),
        update: jest.fn(),
    },
    timelineUpdate: {
        create: jest.fn(),
    },
    user: {
        findUnique: jest.fn(),
        upsert: jest.fn(),
    },
    membership: {
        findUnique: jest.fn(),
        upsert: jest.fn(),
    },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('WarrantyRequestService', () => {
    const mockCreateData = {
        id: 'test-request-123',
        complaint: 'Test complaint',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        email: '<EMAIL>',
        phone: '************',
        rv_vin: '1HGBH41JXMN109186',
        rv_make: 'Keystone',
        rv_model: 'Cougar',
        rv_year: '2025',
        rv_type: 'Travel Trailer',
        component_id: 'comp-123',
    };

    const mockUser = {
        id: 'user-123',
        email: '<EMAIL>',
        first_name: 'Admin',
        last_name: 'User',
    };

    const mockCreatedWarrantyRequest = {
        uuid: 'test-uuid-123',
        status: 'REQUEST_APPROVED',
        created_at: new Date('2025-01-01T10:00:00Z'),
        company_id: 'company-123',
        oem_user_id: 'user-123',
        customer_id: 'customer-123',
        ...mockCreateData,
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('createWarrantyRequest', () => {
        it('should create warranty request with timeline events', async () => {
            // Mock user lookup
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);

            // Mock warranty request creation
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);

            // Mock timeline event creation
            mockPrisma.timelineUpdate.create
                .mockResolvedValueOnce({
                    id: 'te-1',
                    warranty_request_id: 'test-request-123',
                    event_type: TimelineEventType.PREAUTHORIZATION_APPROVED,
                    date: new Date('2025-01-01T10:00:00Z'),
                    updated_by_id: 'user-123',
                    details: {
                        notes: 'Preauthorization approved for warranty request',
                        approved_by: 'user-123'
                    },
                })
                .mockResolvedValueOnce({
                    id: 'te-2',
                    warranty_request_id: 'test-request-123',
                    event_type: TimelineEventType.CUSTOMER_REGISTERED,
                    date: new Date('2025-01-01T10:00:00Z'),
                    updated_by_id: 'user-123',
                    details: {
                        notes: 'Customer registered: John Doe',
                        customer_email: '<EMAIL>',
                        customer_phone: '************'
                    },
                })
                .mockResolvedValueOnce({
                    id: 'te-3',
                    warranty_request_id: 'test-request-123',
                    event_type: TimelineEventType.PREAUTHORIZATION_APPROVED,
                    date: new Date('2025-01-01T10:00:00Z'),
                    updated_by_id: 'user-123',
                    details: {
                        notes: 'Preauthorization approved for warranty request',
                        approved_by: 'user-123'
                    },
                });

            const result = await WarrantyRequestService.createWarrantyRequest(
                mockCreateData,
                'company-123',
                'user-123',
                'Test Company'
            );

            // Verify warranty request was created
            expect(mockPrisma.warrantyRequest.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    id: 'test-request-123',
                    status: 'REQUEST_APPROVED',
                    company_id: 'company-123',
                    oem_user_id: 'user-123',
                }),
                include: {
                    component: true,
                    company: true,
                },
            });

            // Verify timeline events were created
            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledTimes(3);

            // Check PREAUTHORIZATION_APPROVED event
            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    warranty_request_id: 'test-request-123',
                    event_type: TimelineEventType.PREAUTHORIZATION_APPROVED,
                    updated_by_id: 'user-123',
                    details: expect.objectContaining({
                        notes: 'Preauthorization approved for warranty request',
                        approved_by: 'user-123'
                    }),
                }),
            });

            // Check CUSTOMER_REGISTERED event
            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    warranty_request_id: 'test-request-123',
                    event_type: TimelineEventType.CUSTOMER_REGISTERED,
                    updated_by_id: 'user-123',
                    details: expect.objectContaining({
                        notes: 'Customer registered: John Doe',
                        customer_email: '<EMAIL>',
                        customer_phone: '************'
                    }),
                }),
            });

            expect(result).toEqual(mockCreatedWarrantyRequest);
        });

        it('should handle timeline event creation errors gracefully', async () => {
            // Mock user lookup
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);

            // Mock warranty request creation
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);

            // Mock timeline event creation to fail
            mockPrisma.timelineUpdate.create.mockRejectedValue(new Error('Database error'));

            const result = await WarrantyRequestService.createWarrantyRequest(
                mockCreateData,
                'company-123',
                'user-123',
                'Test Company'
            );

            // Verify warranty request was still created
            expect(mockPrisma.warrantyRequest.create).toHaveBeenCalled();

            // Verify timeline events were attempted
            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalled();

            // Should still return the warranty request even if timeline events fail
            expect(result).toEqual(mockCreatedWarrantyRequest);
        });

        it('should set RV make to company name if not provided', async () => {
            const createDataWithoutMake = {
                ...mockCreateData,
                rv_make: undefined,
            };

            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);
            mockPrisma.timelineUpdate.create.mockResolvedValue({} as any);

            await WarrantyRequestService.createWarrantyRequest(
                createDataWithoutMake,
                'company-123',
                'user-123',
                'Test Company'
            );

            expect(mockPrisma.warrantyRequest.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    rv_make: 'Test Company',
                }),
                include: {
                    component: true,
                    company: true,
                },
            });
        });

        it('should not override RV make if already provided', async () => {
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);
            mockPrisma.timelineUpdate.create.mockResolvedValue({} as any);

            await WarrantyRequestService.createWarrantyRequest(
                mockCreateData,
                'company-123',
                'user-123',
                'Test Company'
            );

            expect(mockPrisma.warrantyRequest.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    rv_make: 'Keystone', // Should keep original value
                }),
                include: {
                    component: true,
                    company: true,
                },
            });
        });
    });

    describe('Timeline Event Details', () => {
        it('should include correct details in PREAUTHORIZATION_APPROVED event', async () => {
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);
            mockPrisma.timelineUpdate.create.mockResolvedValue({} as any);

            await WarrantyRequestService.createWarrantyRequest(
                mockCreateData,
                'company-123',
                'user-123',
                'Test Company'
            );

            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    event_type: TimelineEventType.PREAUTHORIZATION_APPROVED,
                    details: expect.objectContaining({
                        notes: 'Preauthorization approved for warranty request',
                        approved_by: 'user-123'
                    }),
                }),
            });
        });

        it('should include correct details in CUSTOMER_REGISTERED event', async () => {
            mockPrisma.user.findUnique.mockResolvedValue(mockUser);
            mockPrisma.warrantyRequest.create.mockResolvedValue(mockCreatedWarrantyRequest);
            mockPrisma.timelineUpdate.create.mockResolvedValue({} as any);

            await WarrantyRequestService.createWarrantyRequest(
                mockCreateData,
                'company-123',
                'user-123',
                'Test Company'
            );

            expect(mockPrisma.timelineUpdate.create).toHaveBeenCalledWith({
                data: expect.objectContaining({
                    event_type: TimelineEventType.CUSTOMER_REGISTERED,
                    details: expect.objectContaining({
                        notes: 'Customer registered: John Doe',
                        customer_email: '<EMAIL>',
                        customer_phone: '************'
                    }),
                }),
            });
        });
    });
});
