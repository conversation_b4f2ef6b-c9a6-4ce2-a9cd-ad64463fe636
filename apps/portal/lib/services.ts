/**
 * Portal app service instances using shared services
 */

import config from "@/config";
import prisma from "@/lib/prisma";
import { createSlackService, InvoiceService } from "@rvhelp/services";
import { getEmailService } from "./services/email.service";

// Configure portal-specific email service
export const emailService = getEmailService();

export const slackService = createSlackService(
    prisma,
    {
        josiahMann: "*********************************************************************************",
        oemJobs: "*********************************************************************************",
    },
    process.env.NODE_ENV === "test"
);

export const invoiceService = new InvoiceService(
    prisma,
    emailService as any, // Type assertion needed for mock service compatibility
    slackService as any, // Type assertion needed for mock service compatibility
    {
        webAppUrl: config.rvhelpUrl,
        portalAppUrl: config.appUrl
        // Using defaults from shared service for platformFeeAmount, notificationEmail, and teamEmails
    }
);


// Export types for convenience
export type { CreateInvoiceInput, CreatePlatformInvoiceInput } from "@rvhelp/services";
