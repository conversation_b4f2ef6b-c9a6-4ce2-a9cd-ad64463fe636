// Portal email service with local templates and shared infrastructure
import config from '@/config';
import prisma from '@/lib/prisma';

import { mockEmailService } from '@/tests/mocks/email-service';
import { SharedEmailService } from '@rvhelp/services';

import { ExtendedWarrantyRequest } from '@/types/warranty';

import {
    PasswordResetEmail,
    passwordResetText,
} from '@/components/email-templates/PasswordResetEmail';

import { WarrantyAuthorizationApprovedEmail } from '@/components/email-templates/WarrantyAuthorizationApprovedEmail';
import { WarrantyAuthorizationFeedbackRequestedEmail } from '@/components/email-templates/WarrantyAuthorizationFeedbackRequestedEmail';
import { WarrantyAuthorizationRejectedEmail } from '@/components/email-templates/WarrantyAuthorizationRejectedEmail';
import { WarrantyRequestEmail } from '@/components/email-templates/WarrantyRequestEmail';

import { formatToE164 } from '@/lib/utils';
import React from 'react';

export interface EmailOptions {
    to: string;
    replyTo?: string;
    cc?: string;
    from?: string;
    subject: string;
    react: React.ReactElement;
    text?: string;
    emailType?: string;
    data?: Record<string, any>;
}

// Portal-specific email service interface
export interface PortalEmailService {
    sendPasswordResetEmail(
        email: string,
        token: string,
        isMobile: boolean
    ): Promise<{ success: boolean; error?: any }>;
    sendWarrantyRequestEmail(
        warrantyRequest: ExtendedWarrantyRequest
    ): Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationApprovedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        approvedHours?: number,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationRejectedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }>;
    sendWarrantyAuthorizationFeedbackRequestedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }>;
    send(options: EmailOptions): Promise<{ success: boolean; result?: any; error?: any }>;
    getEmail(emailId: string): Promise<any>;
}

export interface PortalEmailServiceConfig {
    appUrl: string;
    rvhelpUrl: string;
}

export class PortalEmailServiceImpl extends SharedEmailService implements PortalEmailService {
    constructor(portalConfig: PortalEmailServiceConfig) {
        const sharedConfig = {
            from: config.email.from,
            appUrl: portalConfig.appUrl,
            allowedDomains: config.email.allowedDomains,
            allowedEmails: config.email.allowedEmails,
            resendApiKey: process.env.RESEND_API_KEY,
            isDevelopment: config.isDevelopment,
            formatToE164: formatToE164
        };

        super(prisma, sharedConfig);

        if (!portalConfig.appUrl) {
            throw new Error("appUrl is required in portal config");
        }
        if (!portalConfig.rvhelpUrl) {
            throw new Error("rvhelpUrl is required in portal config");
        }
    }



    // Individual typed methods provide better type safety and clearer APIs

    async sendPasswordResetEmail(email: string, token: string, isMobile: boolean): Promise<{ success: boolean; error?: any }> {
        if (!config.appUrl) {
            throw new Error('NEXT_PUBLIC_APP_URL environment variable is not set');
        }


        const resetUrl = `${config.appUrl}${isMobile ? '/mobile' : ''}/reset-password?token=${token}`;

        return await this.send({
            to: email,
            from: config.email.from,
            subject: 'Reset your password',
            react: PasswordResetEmail({ resetLink: resetUrl }),
            text: passwordResetText(resetUrl),
            emailType: 'password_reset',
        });
    }

    async sendWarrantyRequestEmail(warrantyRequest: ExtendedWarrantyRequest): Promise<{ success: boolean; error?: any }> {
        if (!warrantyRequest.email) {
            console.warn(`No email found for warranty request ${warrantyRequest.id}`);
            return { success: false, error: 'No email found' };
        }

        const company = await prisma.company.findUnique({
            where: {
                id: warrantyRequest.company_id,
            },
        });

        return await this.send({
            to: warrantyRequest.email,
            subject: `Action Required: Schedule Your ${company?.name} Warranty Service`,
            react: WarrantyRequestEmail({ company, warrantyRequest, rvhelpUrl: config.rvhelpUrl }),
            emailType: 'warranty_request',
        });
    }

    async sendWarrantyAuthorizationApprovedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        approvedHours?: number,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        if (!warrantyRequest.email) {
            console.warn(`No email found for warranty authorization approved ${warrantyRequest.id}`);
            return { success: false, error: 'No email found' };
        }

        const company = await prisma.company.findUnique({
            where: {
                id: warrantyRequest.company_id,
            },
        });

        return await this.send({
            to: warrantyRequest.email,
            subject: `Your Warranty Authorization Has Been Approved`,
            react: WarrantyAuthorizationApprovedEmail({
                company,
                warrantyRequest,
                approvedHours,
                updateNotes,
                rvhelpUrl: config.rvhelpUrl,
            }),
            emailType: 'warranty_authorization_approved',
        });
    }

    async sendWarrantyAuthorizationFeedbackRequestedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        if (!warrantyRequest.email) {
            console.warn(`No email found for warranty authorization feedback requested ${warrantyRequest.id}`);
            return { success: false, error: 'No email found' };
        }

        const company = await prisma.company.findUnique({
            where: {
                id: warrantyRequest.company_id,
            },
        });

        return await this.send({
            to: warrantyRequest.email,
            subject: `Feedback Request For Your Warranty Authorization`,
            react: WarrantyAuthorizationFeedbackRequestedEmail({
                company,
                warrantyRequest,
                updateNotes,
                rvhelpUrl: config.rvhelpUrl,
            }),
            emailType: 'warranty_authorization_feedback_requested',
        });
    }

    async sendWarrantyAuthorizationRejectedEmail(
        warrantyRequest: ExtendedWarrantyRequest,
        updateNotes?: string
    ): Promise<{ success: boolean; error?: any }> {
        if (!warrantyRequest.email) {
            console.warn(`No email found for warranty authorization rejected ${warrantyRequest.id}`);
            return { success: false, error: 'No email found' };
        }

        const company = await prisma.company.findUnique({
            where: {
                id: warrantyRequest.company_id,
            },
        });

        return await this.send({
            to: warrantyRequest.email,
            subject: `Your Warranty Authorization Has Been Rejected`,
            react: WarrantyAuthorizationRejectedEmail({
                company,
                warrantyRequest,
                updateNotes,
                rvhelpUrl: config.rvhelpUrl,
            }),
            emailType: 'warranty_authorization_rejected',
        });
    }

    // getEmail method is inherited - no need to override
}

export function getEmailService(): PortalEmailService | typeof mockEmailService {
    if (
        process.env.NODE_ENV === "test" ||
        process.env.USE_MOCK_EMAIL === "true"
    ) {
        return mockEmailService;
    }
    return new PortalEmailServiceImpl({
        appUrl: config.appUrl,
        rvhelpUrl: config.rvhelpUrl
    });
}

export const emailService = getEmailService();
