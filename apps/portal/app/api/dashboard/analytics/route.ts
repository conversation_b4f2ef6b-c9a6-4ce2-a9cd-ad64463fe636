import { createHand<PERSON> } from '@/lib/api/baseHandler';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { getServerSession } from 'next-auth';
import { NextResponse } from 'next/server';

export const GET = createHandler(
    async function () {
        try {
            const session = await getServerSession(authOptions);

            if (!session?.user?.email) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            // Get the current user with their company_id
            const user = await prisma.user.findFirst({
                where: { email: session.user.email },
                select: { id: true, company_id: true, role: true },
            });

            if (!user?.company_id) {
                return NextResponse.json(
                    { error: 'User not associated with a company' },
                    { status: 400 }
                );
            }

            // Only allow ADMIN role to access analytics
            if (user.role !== 'ADMIN') {
                return NextResponse.json(
                    { error: 'Access denied. Admin role required.' },
                    { status: 403 }
                );
            }

            const now = new Date();
            const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
            const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);

            // Get all warranty requests for the company
            const allWarrantyRequests = await prisma.warrantyRequest.findMany({
                where: { company_id: user.company_id },
                include: {
                    component: true,
                    listing: true,
                    customer: true,
                    timeline_updates: {
                        orderBy: { date: 'desc' },
                        take: 1
                    }
                },
                orderBy: { created_at: 'desc' }
            });

            // Calculate metrics
            const totalRequests = allWarrantyRequests.length;
            const thisMonthRequests = allWarrantyRequests.filter(req =>
                new Date(req.created_at) >= startOfMonth
            ).length;
            const lastMonthRequests = allWarrantyRequests.filter(req =>
                new Date(req.created_at) >= lastMonth && new Date(req.created_at) <= lastMonthEnd
            ).length;

            // Calculate revenue metrics (assuming $50 platform fee per job)
            const completedRequests = allWarrantyRequests.filter(req =>
                req.status === 'INVOICE_PAID'
            );
            const thisMonthCompleted = completedRequests.filter(req =>
                new Date(req.updated_at) >= startOfMonth
            ).length;
            const lastMonthCompleted = completedRequests.filter(req =>
                new Date(req.updated_at) >= lastMonth && new Date(req.updated_at) <= lastMonthEnd
            ).length;

            const platformRevenue = completedRequests.length * 50;
            const thisMonthRevenue = thisMonthCompleted * 50;
            const lastMonthRevenue = lastMonthCompleted * 50;

            // Get timeline events for proper time calculations
            const timelineUpdates = await prisma.timelineUpdate.findMany({
                where: {
                    warranty_request_id: {
                        in: allWarrantyRequests.map(req => req.id)
                    }
                },
                orderBy: { date: 'asc' }
            });

            // Group timeline events by warranty request ID
            const timelineByRequestId = timelineUpdates.reduce((acc, event) => {
                if (!acc[event.warranty_request_id]) {
                    acc[event.warranty_request_id] = [];
                }
                acc[event.warranty_request_id].push(event);
                return acc;
            }, {} as Record<string, typeof timelineUpdates>);

            const requestsWithTimeline = allWarrantyRequests.filter(req =>
                timelineByRequestId[req.id] && timelineByRequestId[req.id].length > 0
            );

            // Calculate average RECT (Repair Event Cycle Time) - from PREAUTHORIZATION_APPROVED to INVOICE_PAID
            let totalRECTTime = 0;
            let rectCount = 0;

            for (const req of requestsWithTimeline) {
                const events = timelineByRequestId[req.id];
                const preauthEvent = events.find(e => e.event_type === 'PREAUTHORIZATION_APPROVED');
                const invoicePaidEvent = events.find(e => e.event_type === 'INVOICE_PAID');

                if (preauthEvent && invoicePaidEvent) {
                    const timeDiff = new Date(invoicePaidEvent.date).getTime() - new Date(preauthEvent.date).getTime();
                    totalRECTTime += timeDiff;
                    rectCount++;
                }
            }

            const avgResolutionTime = rectCount > 0
                ? totalRECTTime / rectCount / (1000 * 60 * 60 * 24) // convert to days
                : 0;

            // Calculate match success rate (requests that have TECHNICIAN_ACCEPTED vs total)
            let providerAcceptedCount = 0;

            for (const req of requestsWithTimeline) {
                const events = timelineByRequestId[req.id];
                const acceptedEvent = events.find(e => e.event_type === 'TECHNICIAN_ACCEPTED');
                if (acceptedEvent) {
                    providerAcceptedCount++;
                }
            }

            const matchSuccessRate = totalRequests > 0
                ? (providerAcceptedCount / totalRequests) * 100
                : 0;

            // Calculate average time to acceptance (time from TECHNICIAN_INVITED to TECHNICIAN_ACCEPTED)
            let totalAcceptanceTime = 0;
            let acceptanceCount = 0;

            for (const req of requestsWithTimeline) {
                const events = timelineByRequestId[req.id];
                const invitedEvent = events.find(e => e.event_type === 'TECHNICIAN_INVITED');
                const acceptedEvent = events.find(e => e.event_type === 'TECHNICIAN_ACCEPTED');

                if (invitedEvent && acceptedEvent) {
                    const timeDiff = new Date(acceptedEvent.date).getTime() - new Date(invitedEvent.date).getTime();
                    totalAcceptanceTime += timeDiff;
                    acceptanceCount++;
                }
            }

            const avgTimeToAcceptance = acceptanceCount > 0
                ? totalAcceptanceTime / acceptanceCount / (1000 * 60 * 60) // convert to hours
                : 0;

            // Calculate average registration time (time from PREAUTHORIZATION_APPROVED to CUSTOMER_REGISTERED)
            let totalRegistrationTime = 0;
            let registrationCount = 0;

            for (const req of requestsWithTimeline) {
                const events = timelineByRequestId[req.id];
                const preauthEvent = events.find(e => e.event_type === 'PREAUTHORIZATION_APPROVED');
                const registeredEvent = events.find(e => e.event_type === 'CUSTOMER_REGISTERED');

                if (preauthEvent && registeredEvent) {
                    const timeDiff = new Date(registeredEvent.date).getTime() - new Date(preauthEvent.date).getTime();
                    totalRegistrationTime += timeDiff;
                    registrationCount++;
                }
            }

            const avgRegistrationTime = registrationCount > 0
                ? totalRegistrationTime / registrationCount / (1000 * 60 * 60) // convert to hours
                : 0;

            // Calculate no match cases (requests that were cancelled or rejected)
            const noMatchCases = allWarrantyRequests.filter(req =>
                ['REQUEST_REJECTED', 'JOB_CANCELLED', 'AUTHORIZATION_REJECTED'].includes(req.status)
            );
            const thisWeekNoMatch = noMatchCases.filter(req =>
                new Date(req.updated_at) >= new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            ).length;
            const lastWeekNoMatch = noMatchCases.filter(req =>
                new Date(req.updated_at) >= new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000) &&
                new Date(req.updated_at) < new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000)
            ).length;

            // Calculate pipeline statistics for all statuses
            const allStatuses = [
                'REQUEST_CREATED',
                'REQUEST_APPROVED',
                'REQUEST_REJECTED',
                'JOB_REGISTERED',
                'JOB_REQUESTED',
                'JOB_ACCEPTED',
                'JOB_STARTED',
                'JOB_COMPLETED',
                'JOB_CANCELLED',
                'AUTHORIZATION_REQUESTED',
                'AUTHORIZATION_APPROVED',
                'AUTHORIZATION_REJECTED',
                'AUTHORIZATION_FEEDBACK',
                'PARTS_ORDERED',
                'INVOICE_CREATED',
                'INVOICE_PAID'
            ];

            const pipelineStats = allStatuses.reduce((acc, status) => {
                acc[status] = allWarrantyRequests.filter(req => req.status === status).length;
                return acc;
            }, {} as { [key: string]: number });

            // Calculate average times for pipeline stages (example data for now)
            const pipelineTimes = allStatuses.reduce((acc, status) => {
                // Default example times - these would be calculated from timeline data in the future
                const defaultTimes: { [key: string]: string } = {
                    REQUEST_CREATED: '0 min',
                    REQUEST_APPROVED: '2.5 hrs',
                    REQUEST_REJECTED: '1.2 hrs',
                    JOB_REGISTERED: '15 min',
                    JOB_REQUESTED: '3.5 hrs',
                    JOB_ACCEPTED: '2.1 days',
                    JOB_STARTED: '1.8 days',
                    JOB_COMPLETED: '0.5 days',
                    JOB_CANCELLED: '4.2 hrs',
                    AUTHORIZATION_REQUESTED: '1.5 days',
                    AUTHORIZATION_APPROVED: '2.3 days',
                    AUTHORIZATION_REJECTED: '1.8 days',
                    AUTHORIZATION_FEEDBACK: '3.1 days',
                    PARTS_ORDERED: '4.2 days',
                    INVOICE_CREATED: '1.5 days',
                    INVOICE_PAID: 'Complete'
                };
                acc[status] = defaultTimes[status] || 'N/A';
                return acc;
            }, {} as { [key: string]: string });

            // Get recent no match cases for analysis
            const recentNoMatchCases = noMatchCases
                .slice(0, 10)
                .map(req => ({
                    id: req.id,
                    customer: `${req.first_name} ${req.last_name}`,
                    location: req.location ? (typeof req.location === 'string' ? JSON.parse(req.location)?.address : (req.location as any)?.address) || 'Unknown' : 'Unknown',
                    timeInSystem: req.created_at && req.updated_at
                        ? ((new Date(req.updated_at).getTime() - new Date(req.created_at).getTime()) / (1000 * 60 * 60 * 24)).toFixed(1) + ' days'
                        : 'Unknown',
                    reason: req.status,
                    date: req.updated_at,
                    status: req.status
                }));

            // Get component breakdown for parts analysis
            const componentBreakdown = await prisma.warrantyRequest.groupBy({
                by: ['component_id'],
                where: {
                    company_id: user.company_id,
                    component_id: { not: null },
                    status: 'PARTS_ORDERED'
                },
                _count: {
                    id: true
                },
                orderBy: {
                    _count: {
                        id: 'desc'
                    }
                },
                take: 10
            });

            // Get component details
            const componentIds = componentBreakdown.map(cb => cb.component_id).filter(Boolean);
            const components = await prisma.component.findMany({
                where: { id: { in: componentIds } }
            });

            const topParts = componentBreakdown.map(cb => {
                const component = components.find(c => c.id === cb.component_id);
                return {
                    name: component ? `${component.type} - ${component.manufacturer}` : 'Unknown Component',
                    count: cb._count.id
                };
            });


            return NextResponse.json({
                performanceMetrics: {
                    totalJobsThisMonth: thisMonthRequests,
                    platformRevenueThisMonth: thisMonthRevenue,
                    allTimePlatformRevenue: platformRevenue,
                    avgTimeToAcceptance: avgTimeToAcceptance,
                    avgRepairCycleTime: avgResolutionTime,
                    matchSuccessRate: Math.round(matchSuccessRate),
                    avgRegistrationTime: avgRegistrationTime,
                    noMatchCasesThisWeek: thisWeekNoMatch
                },
                oemHealthMetrics: {
                    avgServiceCallFee: 0, // TODO: Calculate from actual invoice data
                    avgHourlyRate: 0, // TODO: Calculate from actual invoice data
                    avgHoursPerJob: 0, // TODO: Calculate from actual invoice data
                    avgTotalPerJob: 0 // TODO: Calculate from actual invoice data
                },
                financialSummary: {
                    techPayoutsThisMonth: thisMonthCompleted * 50 // platform fee per completed job
                },
                pipeline: {
                    stats: pipelineStats,
                    times: pipelineTimes
                },
                noMatchAnalysis: recentNoMatchCases,
                topParts: topParts,
                trends: {
                    monthlyGrowth: lastMonthRequests > 0
                        ? ((thisMonthRequests - lastMonthRequests) / lastMonthRequests * 100).toFixed(1)
                        : '0',
                    revenueGrowth: lastMonthRevenue > 0
                        ? ((thisMonthRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1)
                        : '0',
                    noMatchTrend: lastWeekNoMatch > 0
                        ? ((thisWeekNoMatch - lastWeekNoMatch) / lastWeekNoMatch * 100).toFixed(1)
                        : '0'
                }
            });

        } catch (error) {
            console.error('Error fetching analytics data:', error);
            return NextResponse.json(
                { error: 'Failed to fetch analytics data' },
                { status: 500 }
            );
        }
    }
);
