import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "../../../lib/services";

// GET - Get all pending platform fee invoices
export const GET = createHandler(
    async function getPlatformInvoices() {
        try {
            const pendingInvoices = await invoiceService.getPendingPlatformInvoices();

            return this.respond({
                success: true,
                invoices: pendingInvoices
            });
        } catch (error) {
            console.error("Error fetching platform fee invoices:", error);
            return this.respond(
                {
                    success: false,
                    error: "Failed to fetch platform fee invoices"
                },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN" // Only admins can view platform fee invoices
    }
);