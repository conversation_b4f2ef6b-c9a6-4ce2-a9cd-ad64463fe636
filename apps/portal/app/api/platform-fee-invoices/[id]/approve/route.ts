import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";

// POST - Approve and send a platform fee invoice
export const POST = createHandler(
    async function approvePlatformInvoice(req, { params }) {
        const { id } = params;

        try {
            // Approve the invoice (sets status to SENT)
            const approvedInvoice = await invoiceService.approvePlatformInvoice(id);

            return this.respond({
                success: true,
                invoice: approvedInvoice,
                message: "Platform fee invoice approved successfully"
            });
        } catch (error) {
            console.error("Error approving platform fee invoice:", error);
            return this.respond(
                {
                    success: false,
                    error: "Failed to approve platform fee invoice"
                },
                500
            );
        }
    },
    {
        requireAuth: true,
        requiredRole: "ADMIN" // Only admins can approve platform fee invoices
    }
);