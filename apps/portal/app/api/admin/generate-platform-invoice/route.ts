import { createHandler } from "@/lib/api/baseHandler";
import { invoiceService } from "@/lib/services";
import { z } from "zod";

const generatePlatformInvoiceSchema = z.object({
    originalInvoiceId: z.string().min(1, "Original invoice ID is required")
});

export const POST = createHandler(
    async function () {
        const { originalInvoiceId } = this.validatedData;
        console.log("🔧 [Platform Invoice API] Received request for invoice ID:", originalInvoiceId);

        try {
            // First, get the original invoice to show details
            console.log("🔧 [Platform Invoice API] Getting original invoice...");
            const originalInvoice = await invoiceService.getInvoiceById(originalInvoiceId);
            if (!originalInvoice) {
                console.error("🔧 [Platform Invoice API] Original invoice not found:", originalInvoiceId);
                return this.respond({
                    success: false,
                    message: "Original invoice not found"
                }, 404);
            }
            console.log("🔧 [Platform Invoice API] Found original invoice:", originalInvoice.id, originalInvoice.invoice_number);

            // Generate platform invoice
            console.log("🔧 [Platform Invoice API] Generating platform invoice...");
            const platformInvoice = await invoiceService.generatePlatformInvoice(originalInvoiceId);

            if (!platformInvoice) {
                console.log("🔧 [Platform Invoice API] No platform invoice generated (likely not a warranty invoice or already exists)");
                return this.respond({
                    success: false,
                    message: "No platform invoice was generated. This may not be a warranty invoice, or a platform invoice may already exist for this warranty request."
                }, 200);
            }

            console.log("🔧 [Platform Invoice API] Successfully generated platform invoice:", platformInvoice.id, platformInvoice.invoice_number);
            return this.respond({
                success: true,
                message: "Platform invoice generated successfully",
                platformInvoice: {
                    id: platformInvoice.id,
                    invoice_number: platformInvoice.invoice_number,
                    amount: platformInvoice.amount,
                    customer_email: platformInvoice.customer_email
                },
                originalInvoice: {
                    id: originalInvoice.id,
                    invoice_number: originalInvoice.invoice_number,
                    amount: originalInvoice.amount,
                    customer_name: originalInvoice.customer_name
                }
            }, 201);
        } catch (error) {
            console.error("🔧 [Platform Invoice API] Error generating platform invoice:", error);
            return this.respond({
                success: false,
                message: error instanceof Error ? error.message : "Failed to generate platform invoice"
            }, 500);
        }
    },
    {
        validateBody: generatePlatformInvoiceSchema,
        requiredRole: "ADMIN" // Only admins can manually generate platform invoices
    }
);
