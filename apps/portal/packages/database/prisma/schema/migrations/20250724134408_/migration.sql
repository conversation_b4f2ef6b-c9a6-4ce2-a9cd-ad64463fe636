-- CreateEnum
CREATE TYPE "CertificationStatus" AS ENUM ('PENDING', 'IN_PROGRESS', 'COMPLETED', 'EXPIRED', 'REVOKED');

-- CreateTable
CREATE TABLE "provider_certifications" (
    "id" TEXT NOT NULL,
    "name" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "description" TEXT,
    "training_content" JSONB,
    "terms_conditions" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_certifications_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "provider_certification_records" (
    "id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "certification_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "status" "CertificationStatus" NOT NULL DEFAULT 'PENDING',
    "opted_out" BOOLEAN NOT NULL DEFAULT false,
    "opted_out_at" TIMESTAMP(3),
    "opted_out_reason" TEXT,
    "training_completed_at" TIMESTAMP(3),
    "terms_accepted_at" TIMESTAMP(3),
    "terms_accepted_by" TEXT,
    "training_progress" JSONB,
    "notes" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "provider_certification_records_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "provider_certifications_name_key" ON "provider_certifications"("name");

-- CreateIndex
CREATE INDEX "provider_certification_records_listing_id_idx" ON "provider_certification_records"("listing_id");

-- CreateIndex
CREATE INDEX "provider_certification_records_certification_id_idx" ON "provider_certification_records"("certification_id");

-- CreateIndex
CREATE INDEX "provider_certification_records_user_id_idx" ON "provider_certification_records"("user_id");

-- CreateIndex
CREATE INDEX "provider_certification_records_status_idx" ON "provider_certification_records"("status");

-- CreateIndex
CREATE UNIQUE INDEX "provider_certification_records_listing_id_certification_id_key" ON "provider_certification_records"("listing_id", "certification_id");

-- AddForeignKey
ALTER TABLE "provider_certification_records" ADD CONSTRAINT "provider_certification_records_listing_id_fkey" FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_certification_records" ADD CONSTRAINT "provider_certification_records_certification_id_fkey" FOREIGN KEY ("certification_id") REFERENCES "provider_certifications"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "provider_certification_records" ADD CONSTRAINT "provider_certification_records_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
