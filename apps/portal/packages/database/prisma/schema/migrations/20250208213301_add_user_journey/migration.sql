-- CreateTable
CREATE TABLE "user_journey_sessions" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "referrer" TEXT,
    "first_interaction" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_interaction" TIMESTAMP(3) NOT NULL,
    "initial_page" TEXT NOT NULL,
    "converted_to_user" BOOLEAN NOT NULL DEFAULT false,

    CONSTRAINT "user_journey_sessions_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "user_journey_events" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "user_id" TEXT,
    "event_type" TEXT NOT NULL,
    "metadata" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "user_journey_events_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_journey_sessions_session_id_idx" ON "user_journey_sessions"("session_id");

-- CreateIndex
CREATE INDEX "user_journey_sessions_user_id_idx" ON "user_journey_sessions"("user_id");

-- CreateIndex
CREATE INDEX "user_journey_events_session_id_idx" ON "user_journey_events"("session_id");

-- CreateIndex
CREATE INDEX "user_journey_events_user_id_idx" ON "user_journey_events"("user_id");

-- CreateIndex
CREATE INDEX "user_journey_events_event_type_idx" ON "user_journey_events"("event_type");

-- AddForeignKey
ALTER TABLE "user_journey_sessions" ADD CONSTRAINT "user_journey_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journey_events" ADD CONSTRAINT "user_journey_events_session_id_fkey" FOREIGN KEY ("session_id") REFERENCES "user_journey_sessions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_journey_events" ADD CONSTRAINT "user_journey_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
