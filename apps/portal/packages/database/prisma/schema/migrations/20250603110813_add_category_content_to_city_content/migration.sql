/*
  Warnings:

  - You are about to drop the column `content` on the `city_content` table. All the data in the column will be lost.
  - You are about to drop the column `title` on the `city_content` table. All the data in the column will be lost.

*/

-- Step 1: Add the new category_content column
ALTER TABLE "city_content" ADD COLUMN "category_content" JSONB DEFAULT '{}';

-- Step 2: Migrate existing data to the new structure
-- Move existing title/content to rv-repair category in the new JSON structure
UPDATE "city_content" 
SET "category_content" = jsonb_build_object(
  'rv-repair', 
  jsonb_build_object(
    'title', COALESCE("title", ''),
    'content', COALESCE("content", '')
  )
)
WHERE "title" IS NOT NULL OR "content" IS NOT NULL;

-- Step 3: Drop the old columns after data migration
ALTER TABLE "city_content" DROP COLUMN "content",
DROP COLUMN "title";
