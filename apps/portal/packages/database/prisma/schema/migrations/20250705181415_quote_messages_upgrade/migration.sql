/*
  Warnings:

  - You are about to drop the column `listing_id` on the `quote_messages` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `quote_messages` table. All the data in the column will be lost.
  - Added the required column `recipient_id` to the `quote_messages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `recipient_type` to the `quote_messages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sender_id` to the `quote_messages` table without a default value. This is not possible if the table is not empty.
  - Changed the type of `sender_type` on the `quote_messages` table. No cast exists, the column would be dropped and recreated, which cannot be done if there is data, since the column is required.

*/
-- CreateEnum
CREATE TYPE "QuoteMessageParticipantType" AS ENUM ('USER', 'PROVIDER');

-- DropForeignKey
ALTER TABLE "quote_messages" DROP CONSTRAINT "quote_messages_listing_id_fkey";

-- DropForeignKey
ALTER TABLE "quote_messages" DROP CONSTRAINT "quote_messages_user_id_fkey";

-- DropIndex
DROP INDEX "quote_messages_listing_id_idx";

-- DropIndex
DROP INDEX "quote_messages_user_id_idx";

-- AlterTable
ALTER TABLE "quote_messages" DROP COLUMN "listing_id",
DROP COLUMN "user_id",
ADD COLUMN     "recipient_id" TEXT NOT NULL,
ADD COLUMN     "recipient_type" "QuoteMessageParticipantType" NOT NULL,
ADD COLUMN     "sender_id" TEXT NOT NULL,
DROP COLUMN "sender_type",
ADD COLUMN     "sender_type" "QuoteMessageParticipantType" NOT NULL;

-- CreateIndex
CREATE INDEX "quote_messages_sender_id_idx" ON "quote_messages"("sender_id");

-- CreateIndex
CREATE INDEX "quote_messages_recipient_id_idx" ON "quote_messages"("recipient_id");
