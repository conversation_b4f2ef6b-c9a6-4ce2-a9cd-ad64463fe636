-- AlterTable
ALTER TABLE "reviews" ADD COLUMN     "is_soft_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "moderated_at" TIMESTAMP(3),
ADD COLUMN     "moderated_by_id" TEXT,
ADD COLUMN     "rejection_reason" TEXT;

-- CreateTable
CREATE TABLE "user_complaints" (
    "id" TEXT NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "complaint_type" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "resolution" TEXT,
    "resolved_at" TIMESTAMP(3),
    "complainant_id" TEXT NOT NULL,
    "reported_id" TEXT NOT NULL,
    "review_id" TEXT,
    "handled_by_id" TEXT,

    CONSTRAINT "user_complaints_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "user_complaints_complainant_id_idx" ON "user_complaints"("complainant_id");

-- CreateIndex
CREATE INDEX "user_complaints_reported_id_idx" ON "user_complaints"("reported_id");

-- CreateIndex
CREATE INDEX "user_complaints_review_id_idx" ON "user_complaints"("review_id");

-- AddForeignKey
ALTER TABLE "reviews" ADD CONSTRAINT "reviews_moderated_by_id_fkey" FOREIGN KEY ("moderated_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_complaints" ADD CONSTRAINT "user_complaints_complainant_id_fkey" FOREIGN KEY ("complainant_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_complaints" ADD CONSTRAINT "user_complaints_reported_id_fkey" FOREIGN KEY ("reported_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_complaints" ADD CONSTRAINT "user_complaints_review_id_fkey" FOREIGN KEY ("review_id") REFERENCES "reviews"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "user_complaints" ADD CONSTRAINT "user_complaints_handled_by_id_fkey" FOREIGN KEY ("handled_by_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
