-- CreateEnum
CREATE TYPE "BlacklistType" AS ENUM ('EMAIL', 'DOMAIN', 'USER_ID', 'IP_ADDRESS');

-- CreateTable
CREATE TABLE "blacklist_entries" (
    "id" TEXT NOT NULL,
    "type" "BlacklistType" NOT NULL,
    "value" TEXT NOT NULL,
    "reason" TEXT NOT NULL,
    "message" TEXT,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_by" TEXT,
    "expires_at" TIMESTAMP(3),
    "is_active" BOOLEAN NOT NULL DEFAULT true,

    CONSTRAINT "blacklist_entries_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "blacklist_entries_value_key" ON "blacklist_entries"("value");

-- CreateIndex
CREATE INDEX "blacklist_entries_type_value_idx" ON "blacklist_entries"("type", "value");
