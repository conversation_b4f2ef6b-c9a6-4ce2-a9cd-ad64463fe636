/*
  Warnings:

  - A unique constraint covering the columns `[payment_transfer_id]` on the table `invoices` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[payment_intent_id]` on the table `invoices` will be added. If there are existing duplicate values, this will fail.

*/
-- AlterTable
ALTER TABLE "invoices" ADD COLUMN     "payment_intent_id" TEXT,
ADD COLUMN     "payment_transfer_id" TEXT;

-- CreateIndex
CREATE UNIQUE INDEX "invoices_payment_transfer_id_key" ON "invoices"("payment_transfer_id");

-- CreateIndex
CREATE UNIQUE INDEX "invoices_payment_intent_id_key" ON "invoices"("payment_intent_id");
