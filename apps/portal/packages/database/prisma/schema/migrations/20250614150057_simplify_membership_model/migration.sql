-- CreateTable
CREATE TABLE "memberships" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "level" "RVHelpMembershipLevel" NOT NULL,
    "member_number" INTEGER,
    "stripe_session_id" TEXT,
    "stripe_subscription_id" TEXT,
    "amount_paid" INTEGER,
    "currency" TEXT DEFAULT 'usd',
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "cancelled_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "memberships_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "memberships_user_id_key" ON "memberships"("user_id");

-- CreateIndex
CREATE INDEX "memberships_level_idx" ON "memberships"("level");

-- CreateIndex
CREATE INDEX "memberships_is_active_idx" ON "memberships"("is_active");

-- CreateIndex
CREATE INDEX "memberships_created_at_idx" ON "memberships"("created_at");

-- AddForeignKey
ALTER TABLE "memberships" ADD CONSTRAINT "memberships_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
