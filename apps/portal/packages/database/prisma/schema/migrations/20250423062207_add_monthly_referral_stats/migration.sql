-- CreateEnum
CREATE TYPE "ReferralEventType" AS ENUM ('VISIT', 'SIGNUP');

-- AlterTable
ALTER TABLE "users" ADD COLUMN     "monthly_referral_stats" JSONB NOT NULL DEFAULT '{}';

-- CreateTable
CREATE TABLE "referral_events" (
    "id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "event_type" "ReferralEventType" NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "referral_events_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "referral_events_user_id_idx" ON "referral_events"("user_id");

-- CreateIndex
CREATE INDEX "referral_events_created_at_idx" ON "referral_events"("created_at");

-- AddF<PERSON>ignKey
ALTER TABLE "referral_events" ADD CONSTRAINT "referral_events_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
