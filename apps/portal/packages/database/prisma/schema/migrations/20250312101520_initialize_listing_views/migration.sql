-- Initialize listing views from listing_interactions
-- This initializes the views count for each listing based on PROFILE_VIEW interactions

-- Note: This is a one-time migration to populate initial values.
-- Going forward, the views will be maintained by the API and cron job.

-- Reset all view counts to zero first
UPDATE listings
SET views = 0, views_last_30_days = 0;

-- Calculate 30 days ago for recent views
DO $$
DECLARE
    thirty_days_ago TIMESTAMP;
BEGIN
    thirty_days_ago := NOW() - INTERVAL '30 days';

    -- Update total views (all-time)
    UPDATE listings
    SET views = (
        SELECT COUNT(*)
        FROM listing_interactions
        WHERE listing_interactions.listing_id = listings.id
        AND listing_interactions.action_type = 'PROFILE_VIEW'
    );

    -- Update recent views (last 30 days)
    UPDATE listings
    SET views_last_30_days = (
        SELECT COUNT(*)
        FROM listing_interactions
        WHERE listing_interactions.listing_id = listings.id
        AND listing_interactions.action_type = 'PROFILE_VIEW'
        AND listing_interactions.created_at >= thirty_days_ago
    );
END $$; 