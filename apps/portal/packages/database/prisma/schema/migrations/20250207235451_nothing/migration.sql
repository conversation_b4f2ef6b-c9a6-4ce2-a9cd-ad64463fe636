-- AlterTable
ALTER TABLE "listing_interaction_sessions" ALTER COLUMN "source" DROP DEFAULT,
ALTER COLUMN "last_interaction" DROP DEFAULT,
ALTER COLUMN "initial_funnel_stage" DROP DEFAULT,
ALTER COLUMN "current_funnel_stage" DROP DEFAULT;

-- CreateIndex
CREATE INDEX "listing_interaction_sessions_session_id_idx" ON "listing_interaction_sessions"("session_id");

-- AddForeignKey
ALTER TABLE "listing_interaction_sessions" ADD CONSTRAINT "listing_interaction_sessions_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE SET NULL ON UPDATE CASCADE;
