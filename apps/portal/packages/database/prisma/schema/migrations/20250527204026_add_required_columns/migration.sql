/*
  Warnings:

  - Added the required column `email` to the `troubleshooting_requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `first_name` to the `troubleshooting_requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `last_name` to the `troubleshooting_requests` table without a default value. This is not possible if the table is not empty.
  - Added the required column `phone` to the `troubleshooting_requests` table without a default value. This is not possible if the table is not empty.

*/
-- AlterTable
ALTER TABLE "listings" ALTER COLUMN "settings_virtual_diagnosis_notifications" SET DEFAULT false;

-- AlterTable
ALTER TABLE "troubleshooting_requests" ADD COLUMN     "email" TEXT NOT NULL,
ADD COLUMN     "first_name" TEXT NOT NULL,
ADD COLUMN     "last_name" TEXT NOT NULL,
ADD COLUMN     "phone" TEXT NOT NULL;
