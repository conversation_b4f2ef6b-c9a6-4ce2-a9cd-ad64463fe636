-- Migration to set primary categories for existing providers
-- Priority: rv-inspection > rv-repair

-- Step 1: Update providers that have rv-inspection selected to set it as primary
UPDATE listings 
SET categories = jsonb_set(
    categories, 
    '{rv-inspection,isPrimary}', 
    'true'::jsonb
)
WHERE categories ? 'rv-inspection' 
  AND (categories->'rv-inspection'->>'selected')::boolean = true;

-- Step 2: Update providers that have rv-repair selected (but not rv-inspection) to set it as primary  
UPDATE listings 
SET categories = jsonb_set(
    categories, 
    '{rv-repair,isPrimary}', 
    'true'::jsonb
)
WHERE categories ? 'rv-repair' 
  AND (categories->'rv-repair'->>'selected')::boolean = true
  AND NOT (
    categories ? 'rv-inspection' 
    AND (categories->'rv-inspection'->>'selected')::boolean = true
  ); 