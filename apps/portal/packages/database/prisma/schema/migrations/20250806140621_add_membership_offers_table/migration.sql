-- CreateTable
CREATE TABLE "public"."membership_offers" (
    "id" TEXT NOT NULL,
    "user_id" TEXT,
    "email" TEXT NOT NULL,
    "offer_type" TEXT NOT NULL,
    "discount_percentage" INTEGER,
    "discount_amount" INTEGER,
    "description" TEXT,
    "is_active" BOOLEAN NOT NULL DEFAULT true,
    "used_at" TIMESTAMP(3),
    "expires_at" TIMESTAMP(3),
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "membership_offers_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "membership_offers_user_id_idx" ON "public"."membership_offers"("user_id");

-- CreateIndex
CREATE INDEX "membership_offers_email_idx" ON "public"."membership_offers"("email");

-- CreateIndex
CREATE INDEX "membership_offers_offer_type_idx" ON "public"."membership_offers"("offer_type");

-- CreateIndex
CREATE INDEX "membership_offers_is_active_idx" ON "public"."membership_offers"("is_active");

-- CreateIndex
CREATE INDEX "membership_offers_expires_at_idx" ON "public"."membership_offers"("expires_at");

-- AddForeignKey
ALTER TABLE "public"."membership_offers" ADD CONSTRAINT "membership_offers_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- Create annual offers for users who created jobs in the last 72 hours
-- This is a one-time data migration to create offers for existing eligible users
INSERT INTO "public"."membership_offers" (
    "id",
    "user_id", 
    "email",
    "offer_type",
    "discount_percentage",
    "description",
    "is_active",
    "used_at",
    "expires_at",
    "created_at",
    "updated_at"
)
SELECT 
    gen_random_uuid()::text as "id",
    u."id" as "user_id",
    u."email",
    'ANNUAL_50_OFF' as "offer_type",
    50 as "discount_percentage",
    'Annual 50% off Pro membership' as "description",
    true as "is_active",
    NULL as "used_at",
    (j."created_at" + INTERVAL '72 hours') as "expires_at",
    NOW() as "created_at",
    NOW() as "updated_at"
FROM "public"."users" u
INNER JOIN "public"."jobs" j ON u."id" = j."user_id"
WHERE 
    u."membership_level" = 'FREE'
    AND j."created_at" >= NOW() - INTERVAL '72 hours'
    AND NOT EXISTS (
        SELECT 1 FROM "public"."membership_offers" mo 
        WHERE mo."user_id" = u."id" 
        AND mo."offer_type" = 'ANNUAL_50_OFF'
        AND mo."is_active" = true
        AND mo."used_at" IS NULL
        AND (mo."expires_at" IS NULL OR mo."expires_at" > NOW())
    )
GROUP BY u."id", u."email", j."created_at";

-- remove the annual_offers_used_count, last_offer_shown_at column from the users table
ALTER TABLE "public"."users" DROP COLUMN "last_offer_shown_at";
ALTER TABLE "public"."users" DROP COLUMN "last_offer_reminder_sent_at";
ALTER TABLE "public"."users" DROP COLUMN "annual_offers_used_count";
