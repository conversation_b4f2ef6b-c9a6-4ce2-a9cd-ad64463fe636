-- CreateTable
CREATE TABLE "virtual_diagnostic_requests" (
    "id" TEXT NOT NULL,
    "user_id" TEXT,
    "first_name" TEXT NOT NULL,
    "last_name" TEXT NOT NULL,
    "email" TEXT NOT NULL,
    "phone" TEXT NOT NULL,
    "rv_type" TEXT NOT NULL,
    "rv_make" TEXT NOT NULL,
    "rv_year" TEXT NOT NULL,
    "rv_model" TEXT NOT NULL,
    "symptoms" TEXT NOT NULL,
    "when_started" TEXT NOT NULL,
    "systems_affected" TEXT NOT NULL,
    "tried_fixes" TEXT NOT NULL,
    "error_messages" TEXT,
    "uploaded_files" JSONB,
    "status" TEXT NOT NULL DEFAULT 'pending',
    "scheduled_for" TIMESTAMP(3),
    "completed_at" TIMESTAMP(3),
    "provider_notes" JSONB,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "virtual_diagnostic_requests_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "virtual_diagnostic_requests_status_idx" ON "virtual_diagnostic_requests"("status");

-- CreateIndex
CREATE INDEX "virtual_diagnostic_requests_user_id_idx" ON "virtual_diagnostic_requests"("user_id");

-- AddForeignKey
ALTER TABLE "virtual_diagnostic_requests" ADD CONSTRAINT "virtual_diagnostic_requests_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "users"("id") ON DELETE CASCADE ON UPDATE CASCADE;
