/*
  Warnings:

  - A unique constraint covering the columns `[slug]` on the table `listings` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[location_id]` on the table `listings` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[listing1_id,listing2_id]` on the table `non_duplicate_pairs` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[stripe_payout_id]` on the table `payouts` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[user_id]` on the table `stripe_connections` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[stripe_account_id]` on the table `stripe_connections` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[email]` on the table `users` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[token]` on the table `verification_tokens` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `funnel_stage` to the `listing_interactions` table without a default value. This is not possible if the table is not empty.

*/
-- First create the sessions table
CREATE TABLE "listing_interaction_sessions" (
    "id" TEXT NOT NULL,
    "session_id" TEXT NOT NULL,
    "listing_id" TEXT NOT NULL,
    "user_id" TEXT,
    "source" TEXT NOT NULL DEFAULT 'direct',
    "first_interaction" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "last_interaction" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "initial_funnel_stage" INTEGER NOT NULL DEFAULT 1,
    "current_funnel_stage" INTEGER NOT NULL DEFAULT 1,
    "converted_to_user" BOOLEAN NOT NULL DEFAULT false,
    CONSTRAINT "listing_interaction_sessions_pkey" PRIMARY KEY ("id")
);

-- Insert sessions for existing interactions
INSERT INTO "listing_interaction_sessions" (id, session_id, listing_id, user_id, source, initial_funnel_stage, current_funnel_stage)
SELECT DISTINCT 
    gen_random_uuid() as id,
    session_id,
    listing_id,
    user_id,
    'direct' as source,
    1 as initial_funnel_stage,
    1 as current_funnel_stage
FROM "listing_interactions";

-- Add funnel_stage to listing_interactions
ALTER TABLE "listing_interactions" ADD COLUMN "funnel_stage" INTEGER;
UPDATE "listing_interactions" SET "funnel_stage" = 1;
ALTER TABLE "listing_interactions" ALTER COLUMN "funnel_stage" SET NOT NULL;

-- Update listing_interactions to reference the new session records
UPDATE "listing_interactions" li 
SET session_id = lis.id
FROM "listing_interaction_sessions" lis
WHERE li.session_id = lis.session_id;

-- Add essential foreign keys and indexes
ALTER TABLE "listing_interactions" ADD CONSTRAINT "listing_interactions_session_id_fkey" 
    FOREIGN KEY ("session_id") REFERENCES "listing_interaction_sessions"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

ALTER TABLE "listing_interaction_sessions" ADD CONSTRAINT "listing_interaction_sessions_listing_id_fkey" 
    FOREIGN KEY ("listing_id") REFERENCES "listings"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Only essential indexes
CREATE INDEX "listing_interaction_sessions_listing_id_idx" ON "listing_interaction_sessions"("listing_id");
CREATE INDEX "listing_interaction_sessions_user_id_idx" ON "listing_interaction_sessions"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "listings_slug_key" ON "listings"("slug");

-- CreateIndex
CREATE UNIQUE INDEX "listings_location_id_key" ON "listings"("location_id");

-- CreateIndex
CREATE UNIQUE INDEX "non_duplicate_pairs_listing1_id_listing2_id_key" ON "non_duplicate_pairs"("listing1_id", "listing2_id");

-- CreateIndex
CREATE UNIQUE INDEX "payouts_stripe_payout_id_key" ON "payouts"("stripe_payout_id");

-- CreateIndex
CREATE INDEX "payouts_user_id_idx" ON "payouts"("user_id");

-- CreateIndex
CREATE INDEX "payouts_status_idx" ON "payouts"("status");

-- CreateIndex
CREATE UNIQUE INDEX "stripe_connections_user_id_key" ON "stripe_connections"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "stripe_connections_stripe_account_id_key" ON "stripe_connections"("stripe_account_id");

-- CreateIndex
CREATE UNIQUE INDEX "users_email_key" ON "users"("email");

-- CreateIndex
CREATE UNIQUE INDEX "verification_tokens_token_key" ON "verification_tokens"("token");

-- CreateIndex
CREATE INDEX "verification_tokens_token_idx" ON "verification_tokens"("token");

-- CreateIndex
CREATE INDEX "verification_tokens_user_id_idx" ON "verification_tokens"("user_id");
