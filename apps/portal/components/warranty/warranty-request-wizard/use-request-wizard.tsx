"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { createId } from "@paralleldrive/cuid2";

import { useRouter } from "next/navigation";
import { useCallback, useEffect, useMemo, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { ExtendedCompany } from "@/types/warranty";
import { useComponentManager } from "../component-manager/use-component-manager";
import { AuthorizationStep } from "./steps/authorization-step";
import { CustomerInfoForm } from "./steps/customer-information-step";
import { IssueDescriptionStep } from "./steps/issue-description-step";
import { LocationForm } from "./steps/location-step";
import { RVDetailsForm } from "./steps/rv-details-step";
import { warrantyRequestSchema } from "./warranty-request-schema";

type WarrantyRequestInput = z.infer<typeof warrantyRequestSchema>;

interface UseWarrantyRequestWizardProps {
	company: ExtendedCompany;
	initialRequest?: any; // Should match ExtendedWarrantyRequest
	onCancel?: () => void;
	onSuccess?: (updatedRequest?: any) => void;
}

export function useWarrantyRequestWizard({
	company,
	initialRequest,
	onCancel,
	onSuccess
}: UseWarrantyRequestWizardProps) {
	const router = useRouter();
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isSuccess, setIsSuccess] = useState(false);
	const [currentStep, setCurrentStep] = useState(1);
	const [currentCompany, setCurrentCompany] = useState(company);
	const [currentRequest, setCurrentRequest] = useState(initialRequest);

	// Component management hook
	const componentManager = useComponentManager({
		company: currentCompany,
		onComponentsChange: (updatedComponents) => {
			setCurrentCompany({ ...currentCompany, components: updatedComponents });
		},
		onComponentCreated: (component) => {
			setCurrentRequest((prev) => ({
				...prev,
				component: component,
				component_id: component.id
			}));
			// Also update the form's component_id field
			form.setValue("component_id", component.id);
		}
	});

	const initialComponent = useMemo(() => {
		const untrackedComponent = company.components.find(
			(c) => c.type === "Untracked"
		);
		if (untrackedComponent) {
			return untrackedComponent;
		} else {
			return undefined;
		}
	}, [company.components]);

	const form = useForm<WarrantyRequestInput>({
		resolver: zodResolver(warrantyRequestSchema),
		mode: "onBlur",
		defaultValues: {
			id: initialRequest?.id || createId(),
			first_name: initialRequest?.first_name || "",
			last_name: initialRequest?.last_name || "",
			email: initialRequest?.email || "",
			phone: initialRequest?.phone || "",
			contact_preference: initialRequest?.contact_preference,
			component_id: initialRequest?.component?.id || undefined,
			location: {
				address: initialRequest?.location?.address || "",
				latitude: initialRequest?.location?.latitude || 0,
				longitude: initialRequest?.location?.longitude || 0
			},
			requires_return: initialRequest?.requires_return || false,
			attachments: initialRequest?.attachments || [],
			notes_to_provider: initialRequest?.notes_to_provider || "",
			rv_make: initialRequest?.rv_make || "",
			rv_vin: initialRequest?.rv_vin || "",
			rv_model: initialRequest?.rv_model || "",
			rv_year: initialRequest?.rv_year || "",
			rv_type: initialRequest?.rv_type || "",
			complaint: initialRequest?.complaint || "",
			cause: initialRequest?.cause || "",
			correction: initialRequest?.correction || "",
			authorization_type: initialRequest?.authorization_type || "GENERAL",
			approved_hours: initialRequest?.approved_hours ?? undefined
		}
	});

	useEffect(() => {
		if (initialRequest) {
			form.reset({
				id: initialRequest?.id || createId(),
				first_name: initialRequest.first_name || "",
				last_name: initialRequest.last_name || "",
				email: initialRequest.email || "",
				phone: initialRequest.phone || "",
				contact_preference: initialRequest.contact_preference,
				location: {
					address: initialRequest.location?.address || "",
					latitude: initialRequest.location?.latitude || 0,
					longitude: initialRequest.location?.longitude || 0
				},
				requires_return: initialRequest?.requires_return || false,
				attachments: initialRequest?.attachments || [],
				component_id: initialRequest.component?.id || undefined,
				notes_to_provider: initialRequest.notes_to_provider || "",
				rv_make: initialRequest.rv_make || "",
				rv_vin: initialRequest.rv_vin || "",
				rv_model: initialRequest.rv_model || "",
				rv_year: initialRequest.rv_year || "",
				rv_type: initialRequest.rv_type || "",
				complaint: initialRequest.complaint || "",
				cause: initialRequest.cause || "",
				correction: initialRequest.correction || "",
				authorization_type: initialRequest.authorization_type || "SPECIFIC",
				approved_hours: initialRequest.approved_hours ?? undefined
			});
		}
	}, [form, initialRequest, initialComponent?.id]);

	const STEPS = useMemo(
		() => [
			{
				id: 1,
				title: "Service Location",
				description: "Where the service will be performed",
				render: (form, company) => (
					<LocationForm form={form} company={company} />
				)
			},
			{
				id: 2,
				title: "Customer Info",
				description: "Personal information and contact details",
				render: (form, company) => (
					<CustomerInfoForm form={form} company={company} />
				)
			},
			{
				id: 3,
				title: "RV Information",
				description: "Vehicle details and specifications",
				render: (form, company) => (
					<RVDetailsForm form={form} company={company} />
				)
			},
			{
				id: 4,
				title: "Service Details",
				description: "Issue description and cost estimates",
				render: (form, company) => (
					<IssueDescriptionStep
						form={form}
						company={currentCompany}
						componentManager={componentManager}
					/>
				)
			},
			{
				id: 5,
				title: "Estimate",
				description: "Estimate of the hours required to complete the job",
				render: (form, company) => (
					<AuthorizationStep form={form} company={company} />
				)
			}
		],
		[form, company, currentCompany, componentManager]
	);

	const validateCurrentStep = useCallback(async () => {
		let fieldsToValidate: (keyof WarrantyRequestInput)[] = [];

		switch (currentStep) {
			case 1:
				fieldsToValidate = ["location"];
				break;
			case 2:
				fieldsToValidate = [
					"id",
					"first_name",
					"last_name",
					"email",
					"phone",
					"contact_preference"
				];
				break;
			case 3:
				fieldsToValidate = [
					"rv_vin",
					"rv_make",
					"rv_model",
					"rv_year",
					"rv_type"
				];
				break;
			case 4:
				fieldsToValidate = ["complaint", "component_id", "notes_to_provider"];
				break;
			case 5:
				fieldsToValidate = ["authorization_type", "approved_hours"];
				break;
		}

		return await form.trigger(fieldsToValidate);
	}, [currentStep, form]);

	const renderCurrentStep = useCallback(() => {
		const step = STEPS.find((step) => step.id === currentStep);
		if (step) {
			return step.render(form, company);
		}
		return null;
	}, [currentStep, form, company]);

	const handleNext = useCallback(async () => {
		const isValid = await validateCurrentStep();
		if (isValid && currentStep < STEPS.length) {
			setCurrentStep(currentStep + 1);
		}
	}, [validateCurrentStep, currentStep]);

	const handlePrevious = useCallback(() => {
		if (currentStep > 1) {
			setCurrentStep(currentStep - 1);
		}
	}, [currentStep]);

	const fillDummyRVValues = useCallback(() => {
		const dummyValues = {
			rv_vin: "1HGBH41JXMN109186", // Example VIN - exactly 17 characters
			rv_make: "Winnebago",
			rv_model: "Adventurer",
			rv_year: "2023",
			rv_type: "Class A"
		};

		form.setValue("rv_vin", dummyValues.rv_vin);
		form.setValue("rv_make", dummyValues.rv_make);
		form.setValue("rv_model", dummyValues.rv_model);
		form.setValue("rv_year", dummyValues.rv_year);
		form.setValue("rv_type", dummyValues.rv_type);
	}, [form]);

	const onSubmit = useCallback(
		async (data: WarrantyRequestInput) => {
			try {
				setIsSubmitting(true);
				let response;
				if (data?.component_id === "") {
					data.component_id = null;
				}
				if (currentRequest?.id) {
					response = await fetch(
						`/api/warranty-requests/${currentRequest.id}`,
						{
							method: "PUT",
							headers: { "Content-Type": "application/json" },
							body: JSON.stringify(data)
						}
					);
				} else {
					response = await fetch("/api/warranty-requests", {
						method: "POST",
						headers: { "Content-Type": "application/json" },
						body: JSON.stringify(data)
					});
				}
				if (!response.ok) throw new Error("Failed to submit warranty request");

				// Get the updated data from the response
				const updatedRequest = await response.json();
				setCurrentRequest(updatedRequest);

				form.reset();
				setIsSuccess(true);
			} catch (error) {
				console.error(error);
				// Optionally show toast
			} finally {
				setIsSubmitting(false);
			}
		},
		[currentRequest, form]
	);

	const handleSubmit = useCallback(() => {
		form.handleSubmit(onSubmit)();
	}, [form, onSubmit]);

	const handleCancel = useCallback(() => {
		if (onCancel) onCancel();
		else router.back();
	}, [onCancel, router]);

	const handleSuccess = useCallback(() => {
		if (onSuccess) onSuccess();
		else router.push("/dashboard");
	}, [onSuccess, router]);

	return {
		// State
		isSubmitting,
		isSuccess,
		currentStep,
		currentRequest,
		form,

		// Actions
		handleNext,
		handlePrevious,
		handleSubmit,
		handleCancel,
		handleSuccess,
		fillDummyRVValues,
		renderCurrentStep,

		// Configuration
		STEPS
	};
}
